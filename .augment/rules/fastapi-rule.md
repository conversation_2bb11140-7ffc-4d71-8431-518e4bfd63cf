---
type: "agent_requested"
description: "fastapi "
---
你是一位 Python、FastAPI 和可扩展 API 开发领域的专家，专门针对 CheeStack 项目架构进行开发。

### 关键原则
- 使用函数式和声明式编程，尽量避免使用类（除了 Pydantic 模型和 Tortoise 模型）
- 更倾向于迭代和模块化，而不是代码重复
- 路由和工具函数优先使用命名导出
- 遵循“接收对象，返回对象”（RORO）模式

### 项目架构规范
#### 完整目录结构
```
project/
├── apps/                        # 应用模块目录
│   ├── app_name1/                 # app名称
│   │   ├── __init__.py           # 模块初始化
│   │   ├── apis.py               # API 路由定义
│   │   ├── models.py             # Tortoise ORM 数据模型
│   │   ├── schema.py             # Pydantic 数据验证模型
│   │   ├── deps.py               # 依赖注入函数
│   │   ├── curd.py               # 数据库 CRUD 操作
│   │   ├── config.py             # 模块配置
│   │   ├── consts.py             # 模块常量
│   │   └── views.py              # 视图函数（可选）
│   │   └── README.md              # app 说明文档
│   ├── app_name2/                 # app名称
│   │   └── ...              # 文件架构参考app_name1
├── core/                          # 核心功能目录
│   ├── __init__.py               # 核心模块初始化
│   ├── config.py                 # 应用配置
│   ├── constants.py              # 全局常量
│   ├── events.py                 # 应用事件处理
│   ├── exceptions.py             # 异常处理器
│   ├── fields.py                 # 自定义字段类型
│   ├── schema.py                 # 基础响应模式
│   ├── http.py                   # HTTP 相关工具
│   ├── middleware.py             # 中间件定义
│   ├── models.py                 # 基础数据模型
│   ├── postgresql.py             # PostgreSQL 数据库配置
│   ├── redis.py                  # Redis 缓存配置
│   ├── responses.py              # 响应处理器
│   ├── routers.py                # 路由配置
│   └── setting.py                # 设置管理
├── static/                        # 静态文件目录
│   └── templates/                # 模板文件
├── assets/                        # 资源文件
│   ├── sources.list              # 系统源配置
│   └── *.mp3                     # 音频文件
├── tests/                         # 测试文件目录
│   ├── __init__.py
│   └── test.py                   # 测试用例
├── main.py                        # 应用入口文件
├── requirements.in                # 依赖需求文件
├── requirements.txt               # 锁定的依赖版本
├── pyproject.toml                 # 项目配置文件
├── dockerfile                     # Docker 配置
├── git-push-version.sh            # 版本推送脚本
└── data_resorted.py              # 数据整理脚本
```


#### 开发规范和最佳实践

##### 模块化设计原则
- **单一职责原则**: 每个模块只负责一个业务领域
- **高内聚低耦合**: 模块内部功能紧密相关，模块间依赖最小化
- **分层架构**: API层、业务逻辑层、数据访问层分离
- **依赖注入**: 使用 FastAPI 的依赖注入系统管理组件

##### 技术栈规范
- **Web框架**: FastAPI (异步高性能)
- **ORM**: Tortoise ORM (异步ORM，类似Django ORM)
- **数据库**: PostgreSQL (主数据库) + Redis (缓存)
- **数据验证**: Pydantic (数据模型和验证)
- **数据库迁移**: Aerich (Tortoise ORM的迁移工具)
- **异步HTTP客户端**: httpx
- **密码加密**: passlib
- **JWT认证**: PyJWT
- **配置管理**: pydantic-settings
- **测试框架**: pytest + pytest-asyncio

##### 文件命名和结构规范

###### 应用模块文件结构
每个应用模块必须包含以下文件：
- `__init__.py`: 模块初始化文件
- `apis.py`: API路由定义，使用APIRouter
- `models.py`: Tortoise ORM数据模型定义
- `schema.py`: Pydantic数据验证和序列化模型
- `deps.py`: 依赖注入函数定义
- `curd.py` 或 `crud.py`: 数据库CRUD操作函数
- `config.py`: 模块特定配置（可选）
- `consts.py`: 模块常量定义（可选）
- `views.py`: 视图函数（可选，用于模板渲染）

###### 核心模块文件说明
- `config.py`: 应用全局配置，使用pydantic-settings
- `constants.py`: 全局常量定义
- `events.py`: FastAPI应用事件处理（startup/shutdown）
- `exceptions.py`: 全局异常处理器
- `fields.py`: 自定义Tortoise ORM字段类型
- `schema.py`: 基础响应模式和通用Pydantic模型
- `http.py`: HTTP状态码和相关工具
- `middleware.py`: 中间件定义和配置
- `models.py`: 基础数据模型和Mixin类
- `postgresql.py`: PostgreSQL数据库连接配置
- `redis.py`: Redis缓存连接配置
- `responses.py`: 自定义响应处理器
- `routers.py`: 路由聚合和配置
- `setting.py`: 设置管理工具


##### 响应格式规范

###### 统一响应格式
```python
# 基础响应模型
class BaseResponseModel(BaseModel):
    code: int = HttpCode.SUCCESS
    msg: str = "success"
    data: Any = []

# 使用resmod函数创建特定响应模型
@router.get("/users", response_model=resmod(list[UserSchemaOut]))
async def get_users():
    users = await User.all()
    return ApiResponse.success(data=users)
```

###### 错误处理规范
```python
# 自定义异常
class Sexception(HTTPException):
    def __init__(self, code: int, msg: str):
        super().__init__(status_code=code, detail=msg)

# 全局异常处理器
@app.exception_handler(Sexception)
async def custom_exception_handler(request: Request, exc: Sexception):
    return JSONResponse(
        status_code=exc.status_code,
        content={"code": exc.status_code, "msg": exc.detail, "data": []}
    )
```

##### 数据库操作规范

###### 事务处理
```python
from tortoise.transactions import in_transaction

async def transfer_money(from_user_id: str, to_user_id: str, amount: float):
    async with in_transaction() as connection:
        # 在事务中执行多个数据库操作
        from_user = await User.get(id=from_user_id).using_db(connection)
        to_user = await User.get(id=to_user_id).using_db(connection)

        from_user.balance -= amount
        to_user.balance += amount

        await from_user.save(using_db=connection)
        await to_user.save(using_db=connection)
```

###### 查询优化
```python
# 使用select_related和prefetch_related优化查询
users = await User.all().select_related("role").prefetch_related("books")

# 使用Q对象进行复杂查询
from tortoise.expressions import Q
users = await User.filter(
    Q(username__icontains="admin") | Q(email__icontains="admin")
)
```

##### 安全规范

###### 密码处理
```python
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    """加密密码"""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)
```

###### JWT认证
```python
import jwt
from datetime import datetime, timedelta

def create_access_token(data: dict, expires_delta: timedelta = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
```

##### 测试规范

###### 单元测试
```python
import pytest
from httpx import AsyncClient
from main import app

@pytest.mark.asyncio
async def test_create_user():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.post(
            "/v1/auth/register",
            json={"username": "testuser", "password": "testpass123"}
        )
    assert response.status_code == 200
    assert response.json()["code"] == 200
```

###### 数据库测试
```python
@pytest.fixture
async def setup_database():
    # 设置测试数据库
    await Tortoise.init(
        db_url="sqlite://:memory:",
        modules={"models": ["apps.auth.models"]}
    )
    await Tortoise.generate_schemas()
    yield
    await Tortoise.close_connections()

@pytest.mark.asyncio
async def test_user_crud(setup_database):
    # 测试用户CRUD操作
    user = await User.create(username="test", password="hashed_password")
    assert user.username == "test"

    retrieved_user = await User.get(id=user.id)
    assert retrieved_user.username == "test"
```

##### 性能优化规范

###### 异步操作
```python
# 使用异步函数处理I/O操作
async def fetch_external_data():
    async with httpx.AsyncClient() as client:
        response = await client.get("https://api.example.com/data")
        return response.json()

# 并发处理多个异步任务
import asyncio

async def process_multiple_users(user_ids: list[str]):
    tasks = [process_user(user_id) for user_id in user_ids]
    results = await asyncio.gather(*tasks)
    return results
```

###### 缓存策略
```python
import redis
from functools import wraps

redis_client = redis.Redis.from_url(settings.REDIS_URL)

def cache_result(expire_time: int = 300):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"

            # 尝试从缓存获取
            cached_result = redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)

            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            redis_client.setex(
                cache_key,
                expire_time,
                json.dumps(result, default=str)
            )
            return result
        return wrapper
    return decorator
```

##### 日志和监控规范

###### 日志配置
```python
import logging
from sutils.log import Slog

# 使用结构化日志
logger = Slog.get_logger(__name__)

@router.post("/login")
async def login(data: AuthModel):
    logger.info("用户登录尝试", extra={
        "username": data.username,
        "ip": request.client.host,
        "user_agent": request.headers.get("user-agent")
    })

    try:
        user = await authenticate_user(data.username, data.password)
        logger.info("用户登录成功", extra={"user_id": user.id})
        return user
    except Exception as e:
        logger.error("用户登录失败", extra={
            "username": data.username,
            "error": str(e)
        })
        raise
```

###### 健康检查
```python
@router.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        await User.first()

        # 检查Redis连接
        redis_client.ping()

        return {"status": "healthy", "timestamp": datetime.utcnow()}
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"Service unhealthy: {str(e)}"
        )
```

##### 部署和配置规范

###### 环境配置
```python
# 使用pydantic-settings管理配置
class Settings(BaseSettings):
    # 数据库配置
    DATABASE_URL: str
    REDIS_URL: str

    # 安全配置
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 应用配置
    DEBUG: bool = False
    CORS_ORIGINS: list[str] = []

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

settings = Settings()
```

###### Docker配置
```dockerfile
FROM python:3.12-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 代码质量检查清单

#### 提交前检查
- [ ] 所有异步函数都使用了`async/await`
- [ ] 数据库操作都进行了适当的错误处理
- [ ] API接口都有完整的类型注解和文档字符串
- [ ] 敏感信息（密码、令牌）都进行了适当的加密和保护
- [ ] 所有路由都有适当的权限检查
- [ ] 代码遵循PEP 8规范
- [ ] 单元测试覆盖率达到要求
- [ ] 没有硬编码的配置信息

#### 性能检查
- [ ] 数据库查询进行了优化（避免N+1问题）
- [ ] 适当使用了缓存机制
- [ ] 大量数据处理使用了分页
- [ ] 文件上传有大小限制
- [ ] API响应时间在可接受范围内

#### 安全检查
- [ ] 输入数据都进行了验证和清理
- [ ] SQL注入防护到位
- [ ] XSS攻击防护到位
- [ ] CSRF攻击防护到位
- [ ] 敏感操作都有权限验证
- [ ] 错误信息不泄露敏感信息

### 常见问题和解决方案

#### 数据库相关
**问题**: Tortoise ORM查询性能问题
**解决方案**:
```python
# 使用select_related避免N+1查询
users = await User.all().select_related("role")

# 使用prefetch_related处理多对多关系
users = await User.all().prefetch_related("books")

# 使用values()只获取需要的字段
user_names = await User.all().values("id", "username")
```

**问题**: 数据库连接池配置
**解决方案**:
```python
# 在core/postgresql.py中配置连接池
TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "host": "localhost",
                "port": "5432",
                "user": "postgres",
                "password": "password",
                "database": "cheestack",
                "minsize": 1,
                "maxsize": 20,
            }
        }
    }
}
```

#### 异步编程相关
**问题**: 混用同步和异步代码
**解决方案**:
```python
# 错误示例
def sync_function():
    return User.all()  # 这会导致错误

# 正确示例
async def async_function():
    return await User.all()

# 如果必须在异步函数中调用同步代码
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def call_sync_in_async():
    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor() as executor:
        result = await loop.run_in_executor(executor, sync_function)
    return result
```

#### 依赖注入相关
**问题**: 依赖注入函数的复用和组合
**解决方案**:
```python
# 基础依赖
async def get_db():
    # 返回数据库连接
    pass

# 组合依赖
async def get_current_user(db = Depends(get_db)):
    # 使用数据库连接获取用户
    pass

async def get_admin_user(current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin required")
    return current_user
```

#### 文件上传处理
**问题**: 大文件上传和存储
**解决方案**:
```python
from fastapi import File, UploadFile
import aiofiles
import os

@router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    # 检查文件大小
    if file.size > 10 * 1024 * 1024:  # 10MB
        raise HTTPException(status_code=413, detail="File too large")

    # 检查文件类型
    allowed_types = ["image/jpeg", "image/png", "application/pdf"]
    if file.content_type not in allowed_types:
        raise HTTPException(status_code=400, detail="File type not allowed")

    # 异步保存文件
    file_path = f"uploads/{file.filename}"
    async with aiofiles.open(file_path, 'wb') as f:
        content = await file.read()
        await f.write(content)

    return {"filename": file.filename, "path": file_path}
```

### 项目特定规范

#### CheeStack项目特殊要求
- **语音处理**: 使用azure-cognitiveservices-speech和edge-tts处理语音相关功能
- **云服务集成**: 支持阿里云和腾讯云服务集成
- **多媒体处理**: 支持音频文件处理和TTS功能
- **学习应用**: 针对教育场景的特殊业务逻辑

#### 工具函数使用规范
```python
# 使用sutils工具包中的实用函数
from sutils.log import Slog
from sutils.string import Sstring
from sutils.datetime import format_datetime
from sutils.validator import Spattern

# 日志记录
logger = Slog.get_logger(__name__)

# 字符串处理
cleaned_text = Sstring.clean_text(user_input)

# 数据验证
if not Spattern.mobile.match(phone_number):
    raise ValueError("Invalid phone number")
```

### 开发工作流程

#### 新功能开发流程
1. **需求分析**: 明确功能需求和API设计
2. **数据模型设计**: 设计Tortoise ORM模型
3. **API接口设计**: 定义路由和Pydantic模型
4. **依赖注入设计**: 设计必要的依赖函数
5. **业务逻辑实现**: 实现CRUD和业务逻辑
6. **单元测试编写**: 编写完整的测试用例
7. **集成测试**: 测试API接口和数据库交互
8. **文档更新**: 更新API文档和代码注释

#### 代码审查要点
- **架构一致性**: 是否遵循项目架构规范
- **代码质量**: 是否符合代码风格规范
- **性能考虑**: 是否有性能优化空间
- **安全性**: 是否存在安全漏洞
- **测试覆盖**: 测试用例是否充分
- **文档完整性**: 代码注释和API文档是否完整

### 维护和监控

#### 日常维护任务
- **数据库维护**: 定期检查数据库性能和索引优化
- **日志监控**: 监控应用日志，及时发现问题
- **性能监控**: 监控API响应时间和系统资源使用
- **安全更新**: 定期更新依赖包，修复安全漏洞
- **备份策略**: 定期备份数据库和重要文件

#### 故障排查指南
1. **检查日志**: 查看应用日志和错误信息
2. **数据库连接**: 检查数据库连接状态
3. **Redis连接**: 检查缓存服务状态
4. **依赖服务**: 检查外部服务可用性
5. **资源使用**: 检查CPU、内存、磁盘使用情况
6. **网络连接**: 检查网络连接和防火墙设置

### 总结

本规范文档涵盖了CheeStack FastAPI项目的完整开发指南，包括：
- 项目架构和文件组织
- 代码风格和最佳实践
- 数据库操作和ORM使用
- API设计和文档规范
- 安全性和性能优化
- 测试和部署流程
- 维护和监控策略

遵循这些规范可以确保代码质量、提高开发效率、降低维护成本，并保持项目的长期可维护性。

