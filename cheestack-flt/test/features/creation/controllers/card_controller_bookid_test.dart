import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/features/creation/controllers/card_controller.dart';
import 'package:cheestack_flt/models/index.dart';

void main() {
  group('CardController BookId 测试', () {
    setUp(() {
      Get.reset();
    });

    tearDown(() {
      Get.reset();
    });

    test('应该能够正确设置bookId', () {
      final controller = CardController();
      const testBookId = 123;
      
      // 设置bookId
      controller.bookId = testBookId;
      
      // 验证bookId被正确设置
      expect(controller.bookId, equals(testBookId));
      
      print('✅ BookId设置测试通过: ${controller.bookId}');
    });

    test('bookId默认应该为null', () {
      final controller = CardController();
      
      // 验证默认值为null
      expect(controller.bookId, isNull);
      
      print('✅ BookId默认值测试通过: ${controller.bookId}');
    });

    test('设置卡片后bookId应该保持不变', () {
      final controller = CardController();
      const testBookId = 456;
      
      // 先设置bookId
      controller.bookId = testBookId;
      
      // 然后设置卡片
      final card = CardModel(id: 1, title: '测试卡片');
      controller.setCard(card);
      
      // 验证bookId没有被改变
      expect(controller.bookId, equals(testBookId));
      expect(controller.card, equals(card));
      
      print('✅ 设置卡片后BookId保持不变测试通过');
    });

    test('验证创建卡片时的逻辑分支', () {
      final controller = CardController();
      
      // 测试没有bookId的情况
      expect(controller.bookId, isNull);
      
      // 设置标题使表单有效
      controller.titleController.text = '测试标题';
      controller.titleController.notifyListeners();
      
      expect(controller.isFormValid, true);
      expect(controller.isNewCard, true);
      
      // 设置bookId
      controller.bookId = 789;
      expect(controller.bookId, equals(789));
      
      print('✅ 创建卡片逻辑分支测试通过');
    });
  });
}
