import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cheestack_flt/features/creation/pages/card_edit_page.dart';
import 'package:cheestack_flt/features/creation/apis/card_service.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/theme.dart';

import 'card_edit_page_test.mocks.dart';

@GenerateMocks([CardService])
void main() {
  group('CardEditPage Widget测试', () {
    late MockCardService mockCardService;

    setUpAll(() async {
      // 初始化 ScreenUtil
      await ScreenUtil.ensureScreenSize();
    });

    setUp(() {
      // 初始化 GetX
      Get.testMode = true;

      // 创建 mock 服务
      mockCardService = MockCardService();

      // 注册 mock 服务
      Get.put<CardService>(mockCardService);
    });

    tearDown(() {
      Get.reset();
    });

    Widget createTestWidget(CardModel card) {
      return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: (context, child) => GetMaterialApp(
          theme: AppTheme.light(),
          home: CardEditPage(card: card),
        ),
      );
    }

    group('界面渲染测试', () {
      testWidgets('应该正确渲染新卡片创建界面', (WidgetTester tester) async {
        final newCard = CardModel();

        await tester.pumpWidget(createTestWidget(newCard));
        await tester.pumpAndSettle();

        // 验证标题
        expect(find.text('创建新卡片'), findsOneWidget);

        // 验证输入框
        expect(find.byType(TextFormField), findsNWidgets(4));

        // 验证标题输入框
        expect(find.text('标题 *'), findsOneWidget);
        expect(find.text('输入卡片标题'), findsOneWidget);

        // 验证问题输入框
        expect(find.text('问题'), findsOneWidget);
        expect(find.text('输入问题（可选）'), findsOneWidget);

        // 验证答案输入框
        expect(find.text('答案'), findsOneWidget);
        expect(find.text('输入答案（可选）'), findsOneWidget);

        // 验证笔记输入框
        expect(find.text('笔记'), findsOneWidget);
        expect(find.text('输入笔记（可选）'), findsOneWidget);

        // 验证保存按钮
        expect(find.text('保存更改'), findsOneWidget);
      });

      testWidgets('应该正确渲染现有卡片编辑界面', (WidgetTester tester) async {
        final existingCard = CardModel(
          id: 1,
          title: '现有标题',
          question: '现有问题',
          answer: '现有答案',
          notes: '现有笔记',
        );

        await tester.pumpWidget(createTestWidget(existingCard));
        await tester.pumpAndSettle();

        // 验证标题
        expect(find.text('编辑卡片'), findsOneWidget);

        // 验证预填充的内容
        expect(find.text('现有标题'), findsOneWidget);
        expect(find.text('现有问题'), findsOneWidget);
        expect(find.text('现有答案'), findsOneWidget);
        expect(find.text('现有笔记'), findsOneWidget);
      });

      testWidgets('应该显示卡片预览区域', (WidgetTester tester) async {
        final card = CardModel(title: '测试标题');

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 验证预览区域
        expect(find.text('卡片预览'), findsOneWidget);
        expect(find.byIcon(Icons.preview), findsOneWidget);
      });

      testWidgets('应该显示区域标题', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 验证区域标题
        expect(find.text('基本信息'), findsOneWidget);
        expect(find.text('卡片内容'), findsOneWidget);
        expect(find.text('补充笔记'), findsOneWidget);

        // 验证图标
        expect(find.byIcon(Icons.info_outline), findsOneWidget);
        expect(find.byIcon(Icons.content_paste), findsOneWidget);
        expect(find.byIcon(Icons.note_alt_outlined), findsAtLeastNWidgets(1));
      });
    });

    group('用户交互测试', () {
      testWidgets('应该能够输入标题并实时预览', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 找到标题输入框并输入文本
        final titleField = find.widgetWithText(TextFormField, '输入卡片标题');
        await tester.enterText(titleField, '新标题');
        await tester.pumpAndSettle();

        // 验证输入的文本在预览区域显示
        expect(find.text('新标题'), findsAtLeastNWidgets(1));

        // 验证预览区域不再显示占位符文本
        expect(find.text('开始输入内容来预览卡片...'), findsNothing);
      });

      testWidgets('应该能够输入问题', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 找到问题输入框并输入文本
        final questionField = find.widgetWithText(TextFormField, '输入问题（可选）');
        await tester.enterText(questionField, '新问题');
        await tester.pumpAndSettle();

        // 验证输入的文本
        expect(find.text('新问题'), findsAtLeastNWidgets(1));
      });

      testWidgets('应该能够输入答案', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 找到答案输入框并输入文本
        final answerField = find.widgetWithText(TextFormField, '输入答案（可选）');
        await tester.enterText(answerField, '新答案');
        await tester.pumpAndSettle();

        // 验证输入的文本
        expect(find.text('新答案'), findsAtLeastNWidgets(1));
      });

      testWidgets('应该能够输入笔记', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 找到笔记输入框并输入文本
        final notesField = find.widgetWithText(TextFormField, '输入笔记（可选）');
        await tester.enterText(notesField, '新笔记');
        await tester.pumpAndSettle();

        // 验证输入的文本
        expect(find.text('新笔记'), findsAtLeastNWidgets(1));
      });

      testWidgets('应该显示字符计数', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 输入一些文本
        final titleField = find.widgetWithText(TextFormField, '输入卡片标题');
        await tester.enterText(titleField, '测试标题');
        await tester.pumpAndSettle();

        // 验证字符计数显示
        expect(find.text('4/100'), findsOneWidget);
      });

      testWidgets('应该显示必填项标识', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 验证必填项标识
        expect(find.text('* 必填项'), findsOneWidget);
      });
    });

    group('保存功能测试', () {
      testWidgets('点击保存按钮应该触发保存', (WidgetTester tester) async {
        final card = CardModel();
        final newCard = CardModel(id: 1, title: '测试标题');

        // 模拟成功保存
        when(mockCardService.createCard(any)).thenAnswer((_) async => newCard);

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 输入标题（必填项）
        final titleField = find.widgetWithText(TextFormField, '输入卡片标题');
        await tester.enterText(titleField, '测试标题');
        await tester.pumpAndSettle();

        // 点击保存按钮
        final saveButton = find.text('保存更改');
        await tester.tap(saveButton);
        await tester.pumpAndSettle();

        // 验证调用了保存方法
        verify(mockCardService.createCard(any)).called(1);
      });

      testWidgets('保存时应该显示加载状态', (WidgetTester tester) async {
        final card = CardModel();

        // 模拟延迟保存
        when(mockCardService.createCard(any)).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return CardModel(id: 1, title: '测试标题');
        });

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 输入标题
        final titleField = find.widgetWithText(TextFormField, '输入卡片标题');
        await tester.enterText(titleField, '测试标题');
        await tester.pump();

        // 点击保存按钮
        final saveButton = find.text('保存更改');
        await tester.tap(saveButton);
        await tester.pump();

        // 验证显示加载状态
        expect(find.text('保存中...'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(1));
      });
    });

    group('表单验证测试', () {
      testWidgets('空标题应该显示验证错误', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 点击保存按钮而不输入标题
        final saveButton = find.text('保存更改');
        await tester.tap(saveButton);
        await tester.pumpAndSettle();

        // 验证显示错误信息
        expect(find.text('标题不能为空'), findsOneWidget);
      });

      testWidgets('超长标题应该显示验证错误', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 输入超长标题
        final titleField = find.widgetWithText(TextFormField, '输入卡片标题');
        final longTitle = 'a' * 101;
        await tester.enterText(titleField, longTitle);
        await tester.pumpAndSettle();

        // 点击保存按钮
        final saveButton = find.text('保存更改');
        await tester.tap(saveButton);
        await tester.pumpAndSettle();

        // 验证显示错误信息
        expect(find.text('标题不能超过100个字符'), findsOneWidget);
      });
    });

    group('GetX状态管理测试', () {
      testWidgets('响应式状态应该正确更新UI', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 验证初始状态：显示占位符文本
        expect(find.text('开始输入内容来预览卡片...'), findsOneWidget);

        // 输入标题
        final titleField = find.widgetWithText(TextFormField, '输入卡片标题');
        await tester.enterText(titleField, '响应式标题');
        await tester.pumpAndSettle();

        // 验证预览区域实时更新
        expect(find.text('响应式标题'), findsAtLeastNWidgets(1));
        expect(find.text('开始输入内容来预览卡片...'), findsNothing);

        // 输入问题
        final questionField = find.widgetWithText(TextFormField, '输入问题（可选）');
        await tester.enterText(questionField, '响应式问题');
        await tester.pumpAndSettle();

        // 验证问题在预览中显示
        expect(find.text('问题: 响应式问题'), findsOneWidget);

        // 输入答案
        final answerField = find.widgetWithText(TextFormField, '输入答案（可选）');
        await tester.enterText(answerField, '响应式答案');
        await tester.pumpAndSettle();

        // 验证答案在预览中显示
        expect(find.text('答案: 响应式答案'), findsOneWidget);
      });

      testWidgets('字符计数应该实时更新', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 找到标题输入框
        final titleField = find.widgetWithText(TextFormField, '输入卡片标题');

        // 输入文本并验证字符计数
        await tester.enterText(titleField, 'Test');
        await tester.pumpAndSettle();

        expect(find.text('4/100'), findsOneWidget);

        // 输入更多文本
        await tester.enterText(titleField, 'Test Title');
        await tester.pumpAndSettle();

        expect(find.text('10/100'), findsOneWidget);
      });
    });

    group('返回导航测试', () {
      testWidgets('点击返回按钮应该显示确认对话框（有未保存更改）', (WidgetTester tester) async {
        final card = CardModel();

        await tester.pumpWidget(createTestWidget(card));
        await tester.pumpAndSettle();

        // 输入一些内容
        final titleField = find.widgetWithText(TextFormField, '输入卡片标题');
        await tester.enterText(titleField, '测试标题');
        await tester.pumpAndSettle();

        // 点击返回按钮
        final backButton = find.byIcon(Icons.arrow_back);
        await tester.tap(backButton);
        await tester.pumpAndSettle();

        // 验证显示确认对话框
        expect(find.text('放弃更改'), findsOneWidget);
        expect(find.text('您有未保存的更改，确定要放弃吗？'), findsOneWidget);
      });
    });
  });
}
