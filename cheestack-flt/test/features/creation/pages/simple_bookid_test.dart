import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/features/creation/pages/card_edit_page.dart';
import 'package:cheestack_flt/features/creation/controllers/card_controller.dart';
import 'package:cheestack_flt/models/index.dart';

void main() {
  group('简单BookId传递测试', () {
    setUpAll(() async {
      // 跳过ScreenUtil初始化，专注于测试bookId传递
    });

    setUp(() {
      Get.reset();
    });

    tearDown(() {
      Get.reset();
    });

    Widget createTestWidget(CardModel card, {int? bookId}) {
      return GetMaterialApp(
        home: CardEditPage(card: card, bookId: bookId),
      );
    }

    testWidgets('验证bookId正确传递到CardController', (WidgetTester tester) async {
      final card = CardModel();
      const testBookId = 123;

      await tester.pumpWidget(createTestWidget(card, bookId: testBookId));
      await tester.pumpAndSettle();

      // 获取CardController实例
      final controller = Get.find<CardController>();

      // 验证bookId被正确设置
      expect(controller.bookId, equals(testBookId));

      print('✅ BookId传递测试通过: ${controller.bookId}');
    });

    testWidgets('验证没有bookId时为null', (WidgetTester tester) async {
      final card = CardModel();

      await tester.pumpWidget(createTestWidget(card));
      await tester.pumpAndSettle();

      // 获取CardController实例
      final controller = Get.find<CardController>();

      // 验证bookId为null
      expect(controller.bookId, isNull);

      print('✅ 无BookId测试通过: ${controller.bookId}');
    });

    testWidgets('验证UI显示正确的页面标题', (WidgetTester tester) async {
      final newCard = CardModel();
      const testBookId = 123;

      await tester.pumpWidget(createTestWidget(newCard, bookId: testBookId));
      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('创建新卡片'), findsOneWidget);
      expect(find.text('卡片预览'), findsOneWidget);

      print('✅ UI显示测试通过');
    });

    testWidgets('验证编辑现有卡片的页面标题', (WidgetTester tester) async {
      final existingCard = CardModel(id: 1, title: '现有卡片');
      const testBookId = 123;

      await tester
          .pumpWidget(createTestWidget(existingCard, bookId: testBookId));
      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('编辑卡片'), findsOneWidget);

      print('✅ 编辑卡片UI测试通过');
    });
  });
}
