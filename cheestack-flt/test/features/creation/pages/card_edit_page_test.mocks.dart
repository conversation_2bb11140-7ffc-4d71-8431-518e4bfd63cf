// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in cheestack_flt/test/features/creation/pages/card_edit_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:cheestack_flt/features/creation/apis/card_service.dart' as _i3;
import 'package:cheestack_flt/models/index.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCardModel_0 extends _i1.SmartFake implements _i2.CardModel {
  _FakeCardModel_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [CardService].
///
/// See the documentation for Mockito's code generation for more information.
class MockCardService extends _i1.Mock implements _i3.CardService {
  MockCardService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.CardModel>> getCardList({
    int? skip = 0,
    int? limit = 20,
    String? search,
    int? bookId,
    String? cardType,
    String? orderBy,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCardList,
          [],
          {
            #skip: skip,
            #limit: limit,
            #search: search,
            #bookId: bookId,
            #cardType: cardType,
            #orderBy: orderBy,
          },
        ),
        returnValue: _i4.Future<List<_i2.CardModel>>.value(<_i2.CardModel>[]),
      ) as _i4.Future<List<_i2.CardModel>>);

  @override
  _i4.Future<_i2.CardModel> createCard(_i2.CardModelCreate? cardData) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCard,
          [cardData],
        ),
        returnValue: _i4.Future<_i2.CardModel>.value(_FakeCardModel_0(
          this,
          Invocation.method(
            #createCard,
            [cardData],
          ),
        )),
      ) as _i4.Future<_i2.CardModel>);

  @override
  _i4.Future<_i2.CardModel> updateCard(
    int? cardId,
    _i2.CardModelCreate? cardData,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCard,
          [
            cardId,
            cardData,
          ],
        ),
        returnValue: _i4.Future<_i2.CardModel>.value(_FakeCardModel_0(
          this,
          Invocation.method(
            #updateCard,
            [
              cardId,
              cardData,
            ],
          ),
        )),
      ) as _i4.Future<_i2.CardModel>);

  @override
  _i4.Future<void> deleteCard(int? cardId) => (super.noSuchMethod(
        Invocation.method(
          #deleteCard,
          [cardId],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i2.CardModel> getCardDetail(int? cardId) => (super.noSuchMethod(
        Invocation.method(
          #getCardDetail,
          [cardId],
        ),
        returnValue: _i4.Future<_i2.CardModel>.value(_FakeCardModel_0(
          this,
          Invocation.method(
            #getCardDetail,
            [cardId],
          ),
        )),
      ) as _i4.Future<_i2.CardModel>);

  @override
  _i4.Future<Map<String, int>> getCardStats() => (super.noSuchMethod(
        Invocation.method(
          #getCardStats,
          [],
        ),
        returnValue: _i4.Future<Map<String, int>>.value(<String, int>{}),
      ) as _i4.Future<Map<String, int>>);

  @override
  _i4.Future<List<_i2.CardModel>> searchCards(String? keyword) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchCards,
          [keyword],
        ),
        returnValue: _i4.Future<List<_i2.CardModel>>.value(<_i2.CardModel>[]),
      ) as _i4.Future<List<_i2.CardModel>>);

  @override
  _i4.Future<List<_i2.CardModel>> getRecentCards({int? limit = 10}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getRecentCards,
          [],
          {#limit: limit},
        ),
        returnValue: _i4.Future<List<_i2.CardModel>>.value(<_i2.CardModel>[]),
      ) as _i4.Future<List<_i2.CardModel>>);

  @override
  _i4.Future<void> batchDeleteCards(List<int>? cardIds) => (super.noSuchMethod(
        Invocation.method(
          #batchDeleteCards,
          [cardIds],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> addCardToBook(
    int? cardId,
    int? bookId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #addCardToBook,
          [
            cardId,
            bookId,
          ],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> removeCardFromBook(int? cardId) => (super.noSuchMethod(
        Invocation.method(
          #removeCardFromBook,
          [cardId],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}
