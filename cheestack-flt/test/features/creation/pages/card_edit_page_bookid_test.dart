import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cheestack_flt/features/creation/pages/card_edit_page.dart';
import 'package:cheestack_flt/features/creation/controllers/card_controller.dart';
import 'package:cheestack_flt/features/creation/apis/card_service.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/services/index.dart';

class MockCardService extends Mock implements CardService {}

class MockCardDataService extends Mock implements CardDataService {}

void main() {
  group('CardEditPage BookId 传递测试', () {
    late MockCardService mockCardService;
    late MockCardDataService mockCardDataService;

    setUpAll(() async {
      await ScreenUtil.ensureScreenSize();
    });

    setUp(() {
      Get.reset();
      mockCardService = MockCardService();
      mockCardDataService = MockCardDataService();

      // 注册mock服务
      Get.put<CardService>(mockCardService);
      Get.put<CardDataService>(mockCardDataService);
    });

    tearDown(() {
      Get.reset();
    });

    Widget createTestWidget(CardModel card, {int? bookId}) {
      return GetMaterialApp(
        home: CardEditPage(card: card, bookId: bookId),
      );
    }

    testWidgets('应该正确传递bookId到CardController', (WidgetTester tester) async {
      final card = CardModel();
      const testBookId = 123;

      await tester.pumpWidget(createTestWidget(card, bookId: testBookId));
      await tester.pumpAndSettle();

      // 获取CardController实例
      final controller = Get.find<CardController>();

      // 验证bookId被正确设置
      expect(controller.bookId, equals(testBookId));

      print('✅ BookId传递测试通过: ${controller.bookId}');
    });

    testWidgets('没有bookId时应该为null', (WidgetTester tester) async {
      final card = CardModel();

      await tester.pumpWidget(createTestWidget(card));
      await tester.pumpAndSettle();

      // 获取CardController实例
      final controller = Get.find<CardController>();

      // 验证bookId为null
      expect(controller.bookId, isNull);

      print('✅ 无BookId测试通过: ${controller.bookId}');
    });

    testWidgets('验证有bookId时控制器状态', (WidgetTester tester) async {
      final card = CardModel();
      const testBookId = 123;

      await tester.pumpWidget(createTestWidget(card, bookId: testBookId));
      await tester.pumpAndSettle();

      // 获取CardController实例
      final controller = Get.find<CardController>();

      // 验证bookId被正确设置
      expect(controller.bookId, equals(testBookId));

      // 输入标题
      controller.titleController.text = '测试卡片';
      controller.titleController.notifyListeners();

      // 验证表单有效
      expect(controller.isFormValid, true);

      // 验证是新卡片
      expect(controller.isNewCard, true);

      print('✅ 有BookId的控制器状态验证完成');
    });

    testWidgets('验证无bookId时控制器状态', (WidgetTester tester) async {
      final card = CardModel();

      await tester.pumpWidget(createTestWidget(card));
      await tester.pumpAndSettle();

      // 获取CardController实例
      final controller = Get.find<CardController>();

      // 验证bookId为null
      expect(controller.bookId, isNull);

      // 输入标题
      controller.titleController.text = '测试卡片';
      controller.titleController.notifyListeners();

      // 验证表单有效
      expect(controller.isFormValid, true);

      // 验证是新卡片
      expect(controller.isNewCard, true);

      print('✅ 无BookId的控制器状态验证完成');
    });
  });
}
