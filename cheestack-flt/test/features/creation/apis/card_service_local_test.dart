import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cheestack_flt/features/creation/apis/card_service.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/models/index.dart';

import 'card_service_local_test.mocks.dart';

@GenerateNiceMocks([
  MockSpec<CardDataService>(),
])
void main() {
  group('CardService 本地数据操作测试', () {
    late CardService cardService;
    late MockCardDataService mockCardDataService;

    setUp(() {
      // 初始化 Get
      Get.testMode = true;

      // 创建 mock 对象
      mockCardDataService = MockCardDataService();

      // 注册服务
      Get.put<CardDataService>(mockCardDataService);

      cardService = CardService();
    });

    tearDown(() {
      Get.reset();
    });

    test('getCardList 应该使用本地数据服务获取书籍卡片', () async {
      // Arrange
      final bookId = 1;
      final expectedCards = [
        CardModel(
          id: 1,
          title: '测试卡片1',
          question: '问题1',
          answer: '答案1',
          type: 'basic',
          typeVersion: 1,
        ),
        CardModel(
          id: 2,
          title: '测试卡片2',
          question: '问题2',
          answer: '答案2',
          type: 'basic',
          typeVersion: 1,
        ),
      ];

      when(mockCardDataService.getBookCards(
        bookId,
        orderBy: anyNamed('orderBy'),
        limit: anyNamed('limit'),
        offset: anyNamed('offset'),
      )).thenAnswer((_) async => expectedCards);

      // Act
      final result = await cardService.getCardList(bookId: bookId);

      // Assert
      expect(result, equals(expectedCards));
      verify(mockCardDataService.getBookCards(
        bookId,
        orderBy: 'created_at DESC',
        limit: 20,
        offset: 0,
      )).called(1);
    });

    test('getCardList 应该支持搜索功能', () async {
      // Arrange
      final keyword = '测试';
      final bookId = 1;
      final expectedCards = [
        CardModel(
          id: 1,
          title: '测试卡片',
          question: '测试问题',
          answer: '测试答案',
          type: 'basic',
          typeVersion: 1,
        ),
      ];

      when(mockCardDataService.searchCards(
        keyword,
        bookId: bookId,
        limit: anyNamed('limit'),
        offset: anyNamed('offset'),
      )).thenAnswer((_) async => expectedCards);

      // Act
      final result = await cardService.getCardList(
        search: keyword,
        bookId: bookId,
      );

      // Assert
      expect(result, equals(expectedCards));
      verify(mockCardDataService.searchCards(
        keyword,
        bookId: bookId,
        limit: 20,
        offset: 0,
      )).called(1);
    });

    test('createCard 应该使用本地数据服务创建卡片', () async {
      // Arrange
      final cardData = CardModelCreate(
        bookId: 1,
        title: '新卡片',
        question: '新问题',
        answer: '新答案',
        type: 'basic',
        typeVersion: 1,
        extra: {},
        cardAssets: [],
      );

      final expectedCard = CardModel(
        id: 123,
        title: cardData.title,
        question: cardData.question,
        answer: cardData.answer,
        type: cardData.type,
        typeVersion: cardData.typeVersion,
        extra: cardData.extra,
      );

      when(mockCardDataService.createCard(
        bookId: cardData.bookId!,
        title: cardData.title!,
        question: cardData.question!,
        answer: cardData.answer!,
        type: cardData.type,
        typeVersion: cardData.typeVersion,
        extra: cardData.extra,
        assets: cardData.cardAssets,
      )).thenAnswer((_) async => expectedCard);

      // Act
      final result = await cardService.createCard(cardData);

      // Assert
      expect(result, equals(expectedCard));
      verify(mockCardDataService.createCard(
        bookId: cardData.bookId!,
        title: cardData.title!,
        question: cardData.question!,
        answer: cardData.answer!,
        type: cardData.type,
        typeVersion: cardData.typeVersion,
        extra: cardData.extra,
        assets: cardData.cardAssets,
      )).called(1);
    });

    test('updateCard 应该使用本地数据服务更新卡片', () async {
      // Arrange
      final cardData = CardModelCreate(
        id: 1,
        bookId: 1,
        title: '更新的卡片',
        question: '更新的问题',
        answer: '更新的答案',
        extra: {'updated': true},
        cardAssets: [],
      );

      final updatedCard = CardModel(
        id: cardData.id,
        title: cardData.title,
        question: cardData.question,
        answer: cardData.answer,
        extra: cardData.extra,
      );

      when(mockCardDataService.updateCard(
        cardData.id!,
        title: cardData.title,
        question: cardData.question,
        answer: cardData.answer,
        extra: cardData.extra,
        assets: cardData.cardAssets,
      )).thenAnswer((_) async => true);

      when(mockCardDataService.getCardById(cardData.id!))
          .thenAnswer((_) async => updatedCard);

      // Act
      final result = await cardService.updateCard(cardData.id!, cardData);

      // Assert
      expect(result, equals(updatedCard));
      verify(mockCardDataService.updateCard(
        cardData.id!,
        title: cardData.title,
        question: cardData.question,
        answer: cardData.answer,
        extra: cardData.extra,
        assets: cardData.cardAssets,
      )).called(1);
      verify(mockCardDataService.getCardById(cardData.id!)).called(1);
    });

    test('deleteCard 应该使用本地数据服务删除卡片', () async {
      // Arrange
      final cardId = 1;

      when(mockCardDataService.deleteCard(cardId))
          .thenAnswer((_) async => true);

      // Act
      await cardService.deleteCard(cardId);

      // Assert
      verify(mockCardDataService.deleteCard(cardId)).called(1);
    });

    test('deleteCard 删除失败时应该抛出异常', () async {
      // Arrange
      final cardId = 1;

      when(mockCardDataService.deleteCard(cardId))
          .thenAnswer((_) async => false);

      // Act & Assert
      expect(
        () => cardService.deleteCard(cardId),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('删除卡片失败'),
        )),
      );
    });

    test('createCard 创建失败时应该抛出异常', () async {
      // Arrange
      final cardData = CardModelCreate(
        bookId: 1,
        title: '新卡片',
        question: '新问题',
        answer: '新答案',
      );

      when(mockCardDataService.createCard(
        bookId: anyNamed('bookId'),
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        type: anyNamed('type'),
        typeVersion: anyNamed('typeVersion'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      )).thenAnswer((_) async => null);

      // Act & Assert
      expect(
        () => cardService.createCard(cardData),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('创建卡片失败'),
        )),
      );
    });

    test('updateCard 更新失败时应该抛出异常', () async {
      // Arrange
      final cardData = CardModelCreate(
        id: 1,
        title: '更新的卡片',
        question: '更新的问题',
        answer: '更新的答案',
      );

      when(mockCardDataService.updateCard(
        any,
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      )).thenAnswer((_) async => false);

      // Act & Assert
      expect(
        () => cardService.updateCard(cardData.id!, cardData),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('更新卡片失败'),
        )),
      );
    });

    test('当CardDataService未注册时应该返回空列表', () async {
      // Arrange
      Get.delete<CardDataService>();

      // Act
      final result = await cardService.getCardList();

      // Assert
      expect(result, isEmpty);
      verifyNever(mockCardDataService.getBookCards(any));
    });
  });
}
