import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/features/creation/controllers/card_controller.dart';
import 'package:cheestack_flt/global.dart';
import 'package:cheestack_flt/shared/utils/index.dart';

/// 手动测试卡片保存功能
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化全局服务
  final global = Global();
  await global.init();
  
  runApp(const ManualCardSaveTestApp());
}

class ManualCardSaveTestApp extends StatelessWidget {
  const ManualCardSaveTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: '手动卡片保存测试',
      home: const ManualCardSaveTestPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class ManualCardSaveTestPage extends StatefulWidget {
  const ManualCardSaveTestPage({super.key});

  @override
  State<ManualCardSaveTestPage> createState() => _ManualCardSaveTestPageState();
}

class _ManualCardSaveTestPageState extends State<ManualCardSaveTestPage> {
  late CardController _controller;
  String _testResult = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller = Get.put(CardController());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('手动卡片保存测试'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              '手动测试卡片保存功能',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            Text('当前BookId: ${_controller.bookId ?? "null"}'),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () => _testWithBookId(),
              child: const Text('测试有BookId的保存'),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () => _testWithoutBookId(),
              child: const Text('测试无BookId的保存'),
            ),
            const SizedBox(height: 20),
            
            if (_isLoading)
              const Center(child: CircularProgressIndicator()),
            
            if (_testResult.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _testResult,
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 测试有BookId的保存
  Future<void> _testWithBookId() async {
    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      Console.log('开始测试有BookId的保存...');
      
      // 设置bookId
      _controller.bookId = 1;
      Console.log('设置bookId = 1');
      
      // 设置卡片内容
      _controller.titleController.text = '测试卡片标题';
      _controller.questionController.text = '测试问题';
      _controller.answerController.text = '测试答案';
      
      // 触发监听器更新
      _controller.titleController.notifyListeners();
      _controller.questionController.notifyListeners();
      _controller.answerController.notifyListeners();
      
      Console.log('设置卡片内容完成');
      Console.log('表单是否有效: ${_controller.isFormValid}');
      Console.log('是否为新卡片: ${_controller.isNewCard}');
      
      // 尝试保存
      Console.log('开始保存卡片...');
      final result = await _controller.saveCard();
      
      setState(() {
        _testResult = '✅ 有BookId保存测试结果: $result\n'
                     'BookId: ${_controller.bookId}\n'
                     '卡片ID: ${_controller.card?.id}\n'
                     '卡片标题: ${_controller.card?.title}';
      });
      
    } catch (e) {
      Console.log('保存失败: $e');
      setState(() {
        _testResult = '❌ 有BookId保存测试失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试无BookId的保存
  Future<void> _testWithoutBookId() async {
    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      Console.log('开始测试无BookId的保存...');
      
      // 清除bookId
      _controller.bookId = null;
      Console.log('清除bookId');
      
      // 设置卡片内容
      _controller.titleController.text = '无BookId测试卡片';
      _controller.questionController.text = '无BookId测试问题';
      _controller.answerController.text = '无BookId测试答案';
      
      // 触发监听器更新
      _controller.titleController.notifyListeners();
      _controller.questionController.notifyListeners();
      _controller.answerController.notifyListeners();
      
      Console.log('设置卡片内容完成');
      Console.log('表单是否有效: ${_controller.isFormValid}');
      Console.log('是否为新卡片: ${_controller.isNewCard}');
      
      // 尝试保存
      Console.log('开始保存卡片...');
      final result = await _controller.saveCard();
      
      setState(() {
        _testResult = '✅ 无BookId保存测试结果: $result\n'
                     'BookId: ${_controller.bookId}\n'
                     '卡片ID: ${_controller.card?.id}\n'
                     '卡片标题: ${_controller.card?.title}';
      });
      
    } catch (e) {
      Console.log('保存失败: $e');
      setState(() {
        _testResult = '❌ 无BookId保存测试失败: $e\n'
                     '这是预期的结果，因为API需要bookId';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    Get.delete<CardController>();
    super.dispose();
  }
}
