import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cheestack_flt/main.dart' as app;
import 'package:cheestack_flt/features/creation/pages/card_edit_page.dart';
import 'package:cheestack_flt/features/creation/apis/card_service.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/global.dart';

class MockCardService extends Mock implements CardService {}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('CardEditPage 集成测试', () {
    late MockCardService mockCardService;

    setUpAll(() async {
      // 初始化 ScreenUtil
      await ScreenUtil.ensureScreenSize();
    });

    setUp(() async {
      // 重置GetX状态
      Get.reset();
      
      // 创建mock服务
      mockCardService = MockCardService();
      
      // 注册mock服务
      Get.put<CardService>(mockCardService);
      
      // 初始化全局依赖（除了CardService，因为我们已经mock了）
      await Global.init();
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('完整的卡片创建流程', (WidgetTester tester) async {
      // 准备mock响应
      final newCard = CardModel(
        id: 1,
        title: '集成测试卡片',
        question: '集成测试问题',
        answer: '集成测试答案',
      );
      
      when(mockCardService.createCard(any))
          .thenAnswer((_) async => newCard);

      // 启动应用并导航到卡片编辑页面
      await tester.pumpWidget(
        GetMaterialApp(
          home: CardEditPage(card: CardModel()),
        ),
      );
      await tester.pumpAndSettle();

      // 验证页面加载
      expect(find.text('创建新卡片'), findsOneWidget);
      expect(find.text('卡片预览'), findsOneWidget);
      expect(find.text('开始输入内容来预览卡片...'), findsOneWidget);

      // 步骤1：输入标题
      final titleField = find.widgetWithText(TextFormField, '输入卡片标题');
      await tester.enterText(titleField, '集成测试卡片');
      await tester.pumpAndSettle();

      // 验证标题在预览中显示
      expect(find.text('集成测试卡片'), findsAtLeastNWidgets(1));
      expect(find.text('开始输入内容来预览卡片...'), findsNothing);
      
      // 验证字符计数
      expect(find.text('7/100'), findsOneWidget);

      // 步骤2：输入问题
      final questionField = find.widgetWithText(TextFormField, '输入问题（可选）');
      await tester.enterText(questionField, '集成测试问题');
      await tester.pumpAndSettle();

      // 验证问题在预览中显示
      expect(find.text('问题: 集成测试问题'), findsOneWidget);

      // 步骤3：输入答案
      final answerField = find.widgetWithText(TextFormField, '输入答案（可选）');
      await tester.enterText(answerField, '集成测试答案');
      await tester.pumpAndSettle();

      // 验证答案在预览中显示
      expect(find.text('答案: 集成测试答案'), findsOneWidget);

      // 步骤4：验证未保存更改指示器显示
      expect(find.text('未保存'), findsOneWidget);

      // 步骤5：保存卡片
      final saveButton = find.text('保存更改');
      expect(saveButton, findsOneWidget);
      
      await tester.tap(saveButton);
      await tester.pump(); // 触发保存状态

      // 验证保存状态显示
      expect(find.text('保存中...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(1));

      await tester.pumpAndSettle(); // 等待保存完成

      // 验证保存调用
      verify(mockCardService.createCard(any)).called(1);
    });

    testWidgets('卡片编辑流程', (WidgetTester tester) async {
      // 准备现有卡片
      final existingCard = CardModel(
        id: 1,
        title: '现有卡片',
        question: '现有问题',
        answer: '现有答案',
        notes: '现有笔记',
      );

      final updatedCard = CardModel(
        id: 1,
        title: '更新后的卡片',
        question: '更新后的问题',
        answer: '更新后的答案',
        notes: '更新后的笔记',
      );

      when(mockCardService.updateCard(any, any))
          .thenAnswer((_) async => updatedCard);

      // 启动应用并导航到卡片编辑页面
      await tester.pumpWidget(
        GetMaterialApp(
          home: CardEditPage(card: existingCard),
        ),
      );
      await tester.pumpAndSettle();

      // 验证页面加载和现有内容
      expect(find.text('编辑卡片'), findsOneWidget);
      expect(find.text('现有卡片'), findsAtLeastNWidgets(1));
      expect(find.text('问题: 现有问题'), findsOneWidget);
      expect(find.text('答案: 现有答案'), findsOneWidget);

      // 修改标题
      final titleField = find.widgetWithText(TextFormField, '输入卡片标题');
      await tester.enterText(titleField, '更新后的卡片');
      await tester.pumpAndSettle();

      // 验证预览更新
      expect(find.text('更新后的卡片'), findsAtLeastNWidgets(1));
      
      // 验证未保存更改指示器
      expect(find.text('未保存'), findsOneWidget);

      // 保存更改
      final saveButton = find.text('保存更改');
      await tester.tap(saveButton);
      await tester.pumpAndSettle();

      // 验证更新调用
      verify(mockCardService.updateCard(1, any)).called(1);
    });

    testWidgets('表单验证流程', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: CardEditPage(card: CardModel()),
        ),
      );
      await tester.pumpAndSettle();

      // 尝试保存空表单
      final saveButton = find.text('保存更改');
      await tester.tap(saveButton);
      await tester.pumpAndSettle();

      // 验证显示验证错误
      expect(find.text('标题不能为空'), findsOneWidget);
      
      // 验证没有调用保存服务
      verifyNever(mockCardService.createCard(any));

      // 输入有效标题
      final titleField = find.widgetWithText(TextFormField, '输入卡片标题');
      await tester.enterText(titleField, '有效标题');
      await tester.pumpAndSettle();

      // 再次尝试保存
      when(mockCardService.createCard(any))
          .thenAnswer((_) async => CardModel(id: 1, title: '有效标题'));
      
      await tester.tap(saveButton);
      await tester.pumpAndSettle();

      // 验证保存成功
      verify(mockCardService.createCard(any)).called(1);
    });

    testWidgets('返回导航确认流程', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: CardEditPage(card: CardModel()),
        ),
      );
      await tester.pumpAndSettle();

      // 输入一些内容
      final titleField = find.widgetWithText(TextFormField, '输入卡片标题');
      await tester.enterText(titleField, '测试内容');
      await tester.pumpAndSettle();

      // 点击返回按钮
      final backButton = find.byIcon(Icons.arrow_back);
      await tester.tap(backButton);
      await tester.pumpAndSettle();

      // 验证确认对话框显示
      expect(find.text('放弃更改'), findsOneWidget);
      expect(find.text('您有未保存的更改，确定要放弃吗？'), findsOneWidget);

      // 点击取消
      final cancelButton = find.text('取消');
      await tester.tap(cancelButton);
      await tester.pumpAndSettle();

      // 验证仍在编辑页面
      expect(find.text('创建新卡片'), findsOneWidget);
      expect(find.text('测试内容'), findsAtLeastNWidgets(1));
    });
  });
}
