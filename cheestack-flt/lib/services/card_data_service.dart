part of services;

/// 卡片数据服务
/// 负责管理卡片数据的本地存储和同步
class CardDataService extends GetxService {
  static CardDataService get to => Get.find();
  
  late final DaoManager _daoManager;
  late final UserDataService _userDataService;
  
  Future<CardDataService> init() async {
    // 等待依赖服务初始化完成
    while (!Get.isRegistered<DaoManager>()) {
      await Future.delayed(const Duration(milliseconds: 10));
    }
    while (!Get.isRegistered<UserDataService>()) {
      await Future.delayed(const Duration(milliseconds: 10));
    }

    _daoManager = DaoManager.to;
    _userDataService = UserDataService.to;
    return this;
  }
  
  /// 获取书籍的所有卡片
  Future<List<CardModel>> getBookCards(int bookId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    try {
      return await _daoManager.cardDao.findByBookId(
        bookId,
        orderBy: orderBy ?? 'created_at DESC',
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      Console.log('Failed to get book cards: $e');
      return [];
    }
  }

  /// 获取用户的所有卡片
  Future<List<CardModel>> getUserCards(
    int userId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    try {
      return await _daoManager.cardDao.findByUserId(
        userId.toString(),
        orderBy: orderBy ?? 'created_at DESC',
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      Console.log('Failed to get user cards: $e');
      return [];
    }
  }
  
  /// 根据ID获取卡片
  Future<CardModel?> getCardById(int cardId) async {
    try {
      return await _daoManager.cardDao.findById(cardId);
    } catch (e) {
      Console.log('Failed to get card by id: $e');
      return null;
    }
  }
  
  /// 搜索卡片
  Future<List<CardModel>> searchCards(String keyword, {
    int? bookId,
    int? limit,
    int? offset,
  }) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return [];
    
    try {
      return await _daoManager.cardDao.search(
        userId,
        keyword,
        bookId: bookId,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      Console.log('Failed to search cards: $e');
      return [];
    }
  }
  
  /// 获取待学习的卡片
  Future<List<CardModel>> getNewCards({int? limit}) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return [];
    
    try {
      return await _daoManager.cardDao.findNewCards(userId, limit: limit);
    } catch (e) {
      Console.log('Failed to get new cards: $e');
      return [];
    }
  }
  
  /// 获取需要复习的卡片
  Future<List<CardModel>> getCardsForReview({int? limit}) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return [];
    
    try {
      return await _daoManager.cardDao.findCardsForReview(userId, limit: limit);
    } catch (e) {
      Console.log('Failed to get cards for review: $e');
      return [];
    }
  }
  
  /// 创建卡片
  Future<CardModel?> createCard({
    required int bookId,
    required String title,
    required String question,
    required String answer,
    String? type,
    int? typeVersion,
    dynamic extra,
    List<CardAsset>? assets,
  }) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return null;
    
    try {
      final card = CardModel(
        type: type ?? 'basic',
        typeVersion: typeVersion ?? 1,
        title: title,
        question: question,
        answer: answer,
        extra: extra,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      final cardId = await _daoManager.cardDao.insertWithIds(card, userId, bookId);
      
      // 添加卡片资源
      if (assets != null && assets.isNotEmpty) {
        await _daoManager.cardAssetDao.insertCardAssets(assets, cardId);
      }
      
      // 同步到服务器（后台进行）
      unawaited(_syncCardToApi(card.copyWith(id: cardId), bookId, assets));
      
      // 返回创建的卡片
      return card.copyWith(id: cardId);
    } catch (e) {
      Console.log('Failed to create card: $e');
      return null;
    }
  }
  
  /// 更新卡片
  Future<bool> updateCard(int cardId, {
    String? title,
    String? question,
    String? answer,
    dynamic extra,
    List<CardAsset>? assets,
  }) async {
    try {
      // 检查卡片是否存在
      final existingCard = await _daoManager.cardDao.findById(cardId);
      if (existingCard == null) {
        throw Exception('卡片不存在');
      }
      
      await _daoManager.cardDao.updateContent(
        cardId,
        title: title,
        question: question,
        answer: answer,
        extra: extra,
      );
      
      // 更新卡片资源
      if (assets != null) {
        await _daoManager.cardAssetDao.deleteByCardId(cardId);
        if (assets.isNotEmpty) {
          await _daoManager.cardAssetDao.insertCardAssets(assets, cardId);
        }
      }
      
      // 同步到服务器（后台进行）
      unawaited(_syncCardUpdateToApi(
          cardId,
          {
        if (title != null) 'title': title,
        if (question != null) 'question': question,
        if (answer != null) 'answer': answer,
        if (extra != null) 'extra': extra,
          },
          assets));
      
      Console.log('Card updated successfully');
      return true;
    } catch (e) {
      Console.log('Failed to update card: $e');
      return false;
    }
  }
  
  /// 删除卡片
  Future<bool> deleteCard(int cardId) async {
    try {
      // 检查卡片是否存在
      final existingCard = await _daoManager.cardDao.findById(cardId);
      if (existingCard == null) {
        throw Exception('卡片不存在');
      }
      
      // 删除卡片资源
      await _daoManager.cardAssetDao.deleteByCardId(cardId);
      
      // 删除学习记录
      await _daoManager.studyRecordDao.deleteByCardId(cardId);
      
      // 删除卡片
      await _daoManager.cardDao.delete(cardId);
      
      // 同步到服务器（后台进行）
      unawaited(_syncCardDeleteToApi(cardId));
      
      Console.log('Card deleted successfully');
      return true;
    } catch (e) {
      Console.log('Failed to delete card: $e');
      return false;
    }
  }
  
  /// 批量删除卡片
  Future<bool> batchDeleteCards(List<int> cardIds) async {
    try {
      if (cardIds.isEmpty) return true;
      
      // 删除卡片资源
      for (final cardId in cardIds) {
        await _daoManager.cardAssetDao.deleteByCardId(cardId);
        await _daoManager.studyRecordDao.deleteByCardId(cardId);
      }
      
      // 批量删除卡片
      await _daoManager.cardDao.batchDelete(cardIds);
      
      // 同步到服务器（后台进行）
      for (final cardId in cardIds) {
        unawaited(_syncCardDeleteToApi(cardId));
      }
      
      Console.log('Cards deleted successfully');
      return true;
    } catch (e) {
      Console.log('Failed to batch delete cards: $e');
      return false;
    }
  }
  
  /// 移动卡片到其他书籍
  Future<bool> moveCardsToBook(List<int> cardIds, int targetBookId) async {
    try {
      if (cardIds.isEmpty) return true;
      
      await _daoManager.cardDao.batchMoveToBook(cardIds, targetBookId);
      
      // 同步到服务器（后台进行）
      for (final cardId in cardIds) {
        _syncCardUpdateToApi(cardId, {'book_id': targetBookId}, null);
      }
      
      Console.log('Cards moved successfully');
      return true;
    } catch (e) {
      Console.log('Failed to move cards: $e');
      return false;
    }
  }
  
  /// 获取卡片资源
  Future<List<CardAsset>> getCardAssets(int cardId) async {
    try {
      return await _daoManager.cardAssetDao.findByCardId(cardId);
    } catch (e) {
      Console.log('Failed to get card assets: $e');
      return [];
    }
  }
  
  /// 获取卡片统计信息
  Future<Map<String, dynamic>> getCardStats(int cardId) async {
    try {
      final stats = <String, dynamic>{};
      
      // 获取资源数量
      final assetCount = await _daoManager.cardAssetDao.countByCardId(cardId);
      stats['asset_count'] = assetCount;
      
      // 获取学习记录数量
      final recordCount = await _daoManager.studyRecordDao.count(
        where: 'card_id = ?',
        whereArgs: [cardId],
      );
      stats['study_count'] = recordCount;
      
      // 获取最后学习时间
      final lastRecord = await _daoManager.studyRecordDao.findLatestByCardId(cardId);
      if (lastRecord != null) {
        stats['last_study_time'] = lastRecord.reviewTime.toIso8601String();
        stats['last_rating'] = lastRecord.rating;
      }
      
      return stats;
    } catch (e) {
      Console.log('Failed to get card stats: $e');
      return {};
    }
  }
  
  /// 从API同步卡片数据
  Future<bool> syncCardsFromApi({int? bookId}) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return false;
    
    try {
      Console.log('Syncing cards from API...');
      
      int skip = 0;
      const int limit = 50;
      bool hasMore = true;
      
      while (hasMore) {
        final queryParams = {
          'skip': skip,
          'limit': limit,
          'order': '-created_at',
        };
        
        if (bookId != null) {
          queryParams['filters'] = 'books__id:$bookId';
        }
        
        final response = await OxHttp.to.get(
          '/api/v1/cards',
          queryParameters: queryParams,
        );
        
        if (response.statusCode == 200) {
          final List<dynamic> cardsData = response.data['data'] ?? [];
          
          if (cardsData.isEmpty) {
            hasMore = false;
            break;
          }
          
          for (final cardData in cardsData) {
            final card = CardModel(
              id: cardData['id'],
              type: cardData['type'],
              typeVersion: cardData['type_version'],
              title: cardData['title'],
              question: cardData['question'],
              answer: cardData['answer'],
              extra: cardData['extra'],
              scheduleId: cardData['schedule_id'],
              createdAt: cardData['created_at'] != null 
                  ? DateTime.parse(cardData['created_at']) 
                  : null,
              updatedAt: cardData['updated_at'] != null 
                  ? DateTime.parse(cardData['updated_at']) 
                  : null,
            );
            
            // 获取书籍ID
            final cardBookId = cardData['books']?[0]?['id'] ?? bookId;
            if (cardBookId == null) continue;
            
            // 检查卡片是否已存在
            final existingCard = await _daoManager.cardDao.findById(card.id!);
            if (existingCard != null) {
              await _daoManager.cardDao.update(card);
            } else {
              await _daoManager.cardDao.insertWithIds(card, userId, cardBookId);
            }
            
            // 同步卡片资源
            if (cardData['card_assets'] != null) {
              await _syncCardAssetsFromData(cardData['card_assets'], card.id!);
            }
          }
          
          skip += limit;
          if (cardsData.length < limit) {
            hasMore = false;
          }
        } else {
          hasMore = false;
        }
      }
      
      Console.log('Cards synced from API successfully');
      return true;
    } catch (e) {
      Console.log('Failed to sync cards from API: $e');
      return false;
    }
  }
  
  /// 同步卡片资源数据
  Future<void> _syncCardAssetsFromData(List<dynamic> assetsData, int cardId) async {
    try {
      // 先删除现有的卡片资源
      await _daoManager.cardAssetDao.deleteByCardId(cardId);
      
      for (final assetData in assetsData) {
        final asset = CardAsset(
          id: assetData['id'],
          cardId: cardId,
          assetId: assetData['asset_id'],
          isCorrect: assetData['is_correct'] ?? true,
          type: assetData['type'],
          text: assetData['text'],
          url: assetData['url'],
          name: assetData['name'],
        );
        
        await _daoManager.cardAssetDao.insertCardAsset(asset);
      }
    } catch (e) {
      Console.log('Failed to sync card assets: $e');
    }
  }
  
  /// 同步卡片到API
  Future<void> _syncCardToApi(CardModel card, int bookId, List<CardAsset>? assets) async {
    try {
      final data = {
        'type': card.type,
        'type_version': card.typeVersion,
        'title': card.title,
        'question': card.question,
        'answer': card.answer,
        'extra': card.extra,
        'books': [bookId],
      };
      
      if (assets != null && assets.isNotEmpty) {
        data['card_assets'] = assets.map((asset) => {
          'asset_id': asset.assetId,
          'is_correct': asset.isCorrect,
          'type': asset.type,
          'text': asset.text,
          'url': asset.url,
          'name': asset.name,
        }).toList();
      }
      
      await OxHttp.to.post('/api/v1/cards', data: data);
      Console.log('Card synced to API: ${card.title}');
    } catch (e) {
      Console.log('Failed to sync card to API: $e');
    }
  }
  
  /// 同步卡片更新到API
  Future<void> _syncCardUpdateToApi(int cardId, Map<String, dynamic> data, List<CardAsset>? assets) async {
    try {
      if (assets != null) {
        data['card_assets'] = assets.map((asset) => {
          'asset_id': asset.assetId,
          'is_correct': asset.isCorrect,
          'type': asset.type,
          'text': asset.text,
          'url': asset.url,
          'name': asset.name,
        }).toList();
      }
      
      await OxHttp.to.put('/api/v1/cards/$cardId', data: data);
      Console.log('Card update synced to API: $cardId');
    } catch (e) {
      Console.log('Failed to sync card update to API: $e');
    }
  }
  
  /// 同步卡片删除到API
  Future<void> _syncCardDeleteToApi(int cardId) async {
    try {
      await OxHttp.to.delete('/api/v1/cards/$cardId');
      Console.log('Card deletion synced to API: $cardId');
    } catch (e) {
      Console.log('Failed to sync card deletion to API: $e');
    }
  }

  /// 手动触发卡片同步
  Future<bool> manualSync({int? bookId}) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return false;

    try {
      Console.log('Starting manual card sync...');

      // 1. 同步本地变更到服务器
      // TODO: 实现本地卡片同步到API的逻辑
      Console.log('Local cards sync to API not implemented yet');

      // 2. 从服务器同步更新
      await syncCardsFromApi(bookId: bookId);

      Console.log('Manual card sync completed successfully');
      return true;
    } catch (e) {
      Console.log('Manual card sync failed: $e');
      return false;
    }
  }
}
