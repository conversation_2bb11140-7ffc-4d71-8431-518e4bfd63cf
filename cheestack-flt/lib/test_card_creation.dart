import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/features/creation/pages/card_edit_page.dart';
import 'package:cheestack_flt/global.dart';

/// 测试卡片创建功能的页面
class TestCardCreationPage extends StatelessWidget {
  const TestCardCreationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('测试卡片创建'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              '测试卡片创建功能',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _testCreateNewCard(),
              child: const Text('测试创建新卡片（有bookId）'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () => _testCreateNewCardWithoutBookId(),
              child: const Text('测试创建新卡片（无bookId）'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () => _testEditExistingCard(),
              child: const Text('测试编辑现有卡片'),
            ),
            const SizedBox(height: 20),
            const Text(
              '说明：',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const Text(
              '1. 第一个按钮测试有bookId的情况，应该能正常保存\n'
              '2. 第二个按钮测试无bookId的情况，会显示之前的错误\n'
              '3. 第三个按钮测试编辑现有卡片的情况',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 测试创建新卡片（有bookId）
  void _testCreateNewCard() {
    final newCard = CardModel();
    Get.to(() => CardEditPage(
          card: newCard,
          bookId: 1, // 提供一个测试用的bookId
        ));
  }

  /// 测试创建新卡片（无bookId）
  void _testCreateNewCardWithoutBookId() {
    final newCard = CardModel();
    Get.to(() => CardEditPage(
          card: newCard,
          // 不提供bookId，应该会出现之前的错误
        ));
  }

  /// 测试编辑现有卡片
  void _testEditExistingCard() {
    final existingCard = CardModel(
      id: 123,
      title: '现有卡片标题',
      question: '现有问题',
      answer: '现有答案',
    );
    Get.to(() => CardEditPage(
          card: existingCard,
          bookId: 1, // 提供bookId
        ));
  }
}

/// 主应用入口，用于测试
class TestCardCreationApp extends StatelessWidget {
  const TestCardCreationApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: '卡片创建测试',
      home: const TestCardCreationPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// 测试入口函数
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化全局服务
  final global = Global();
  await global.init();

  runApp(const TestCardCreationApp());
}
