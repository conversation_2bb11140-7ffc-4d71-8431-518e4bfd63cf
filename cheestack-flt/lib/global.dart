import 'package:cheestack_flt/apis/index.dart';
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/services/dao/dao_manager.dart';
import 'package:cheestack_flt/shared/widgets/ads/index.dart';
import 'package:cheestack_flt/shared/widgets/index.dart';
import 'package:cheestack_flt/shared/utils/oxhttp/index.dart';
import 'package:cheestack_flt/shared/utils/http_client/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
// API服务导入
import 'package:cheestack_flt/features/auth/apis/auth_api.dart';
import 'package:cheestack_flt/features/creation/apis/book_api.dart';
import 'package:cheestack_flt/features/creation/apis/card_api.dart';
import 'package:cheestack_flt/features/creation/apis/book_service.dart';
import 'package:cheestack_flt/features/creation/apis/card_service.dart';
import 'package:cheestack_flt/features/study/services/study_api.dart';
import 'package:cheestack_flt/features/study/services/review_api.dart';
import 'package:cheestack_flt/features/profile/apis/profile_api.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gromore/flutter_gromore.dart';
// import 'package:flutter_gromore/flutter_gromore.dart';
import 'package:get/get.dart';
import 'controllers/index.dart';
import 'services/index.dart';

class Global {
  Future<void> init() async {
    WidgetsFlutterBinding.ensureInitialized();

    /// 直屏模式
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    await Future.wait([
      Get.putAsync<StorageService>(() => StorageService().init()),
      Get.putAsync<SaudioService>(() => SaudioService().init()),
      Get.putAsync<DatabaseService>(() => DatabaseService().init()),
      Get.putAsync<DaoManager>(() => DaoManager().init()),
    ]).whenComplete(() async {
      // 初始化旧的OxHttp（保持兼容性）
      OxHttp().init(dioConfig: HttpConfig(baseUrl: baseUrl));

      // 初始化新的HTTP客户端
      HttpClient.instance.init(HttpClientConfig(
        baseUrl: baseUrl,
        enableCache: true,
        enableRetry: true,
        maxRetries: 3,
      ));

      // 先注册API服务（在控制器之前）
      Get.lazyPut<AuthApiService>(() => AuthApiService());
      Get.lazyPut<BookApiService>(() => BookApiService());
      Get.lazyPut<CardApiService>(() => CardApiService());
      Get.lazyPut<StudyApiService>(() => StudyApiService());
      Get.lazyPut<ReviewApiService>(() => ReviewApiService());
      Get.lazyPut<ProfileApiService>(() => ProfileApiService());

      // 注册业务服务层（依赖API服务）
      Get.lazyPut<BookService>(() => BookService());
      Get.lazyPut<CardService>(() => CardService());

      // 然后注册控制器（依赖API服务）
      Get.put<ConfigStore>(ConfigStore());
      Get.put<AuthController>(AuthController());
      Get.put<OxAudioController>(OxAudioController());
      Get.put<SupgradeController>(SupgradeController());

      // 等待所有异步服务初始化完成
      await Future.wait([
        Get.putAsync<SyncService>(() => SyncService().init()),
        Get.putAsync<ApiSyncService>(() => ApiSyncService().init()),
        Get.putAsync<UserDataService>(() => UserDataService().init()),
        Get.putAsync<BookDataService>(() => BookDataService().init()),
        Get.putAsync<CardDataService>(() => CardDataService().init()),
        Get.putAsync<StudyDataService>(() => StudyDataService().init()),
        Get.putAsync<AppInitService>(() => AppInitService().init()),
      ]);
    });
  }

  static initGromore() async {
    if (!FlutterGromore.isInit) {
      try {
        bool rst = await FlutterGromore.initSDK(
          appId: GroMoreAdConfig.appId,
          appName: GroMoreAdConfig.appName,
          debug: false,
          // 是否使用聚合功能
          useMediation: true,
        );
        Console.log("initGromore: $rst");
      } catch (e) {
        Console.log(e);
      }
    }
  }
}
