import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/widgets/index.dart';
import '../controllers/card_controller.dart';
import '../widgets/book_selector.dart';

class CardEditPage extends StatefulWidget {
  final CardModel card;
  final int? bookId;

  const CardEditPage({super.key, required this.card, this.bookId});

  @override
  _CardEditPageState createState() => _CardEditPageState();
}

class _CardEditPageState extends State<CardEditPage> {
  late CardController _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.put(CardController());

    // 设置bookId（如果提供）- 必须在setCard之前设置
    if (widget.bookId != null) {
      _controller.bookId = widget.bookId;
      Console.log('CardEditPage: 设置bookId = ${widget.bookId}');
    } else {
      Console.log('CardEditPage: 没有提供bookId');
    }

    _controller.setCard(widget.card);
  }

  @override
  void dispose() {
    Get.delete<CardController>();
    super.dispose();
  }

  Future<void> _saveCard() async {
    final success = await _controller.saveCard();
    if (success) {
      Get.back(result: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomActions(),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: OxText(_controller.isNewCard ? '创建新卡片' : '编辑卡片'),
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () async {
          final canPop = await _controller.showDiscardChangesDialog();
          if (canPop) {
            Get.back();
          }
        },
      ),
      actions: [
        // 字符计数指示器
        Obx(() {
          return _controller.hasUnsavedChanges()
              ? Container(
                  margin: EdgeInsets.only(right: 8.w),
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withAlpha(51),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    '未保存',
                    style: TextStyle(
                      fontSize: AppTheme.fontSmall,
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                )
              : const SizedBox.shrink();
        }),
        // 保存按钮
        Obx(() {
          return IconButton(
            icon: _controller.isSaving
                ? SizedBox(
                    width: 20.w,
                    height: 20.h,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  )
                : const Icon(Icons.save),
            onPressed: _controller.isSaving ? null : _saveCard,
            tooltip: '保存',
          );
        }),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppTheme.paddingLarge.left),
      child: Form(
        key: _controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 卡片预览区域
            _buildCardPreview(),
            SizedBox(height: AppTheme.spacingLarge),

            // 基本信息区域
            _buildSectionHeader('基本信息', Icons.info_outline),
            SizedBox(height: AppTheme.spacingMedium),
            _buildBookSelector(),
            SizedBox(height: AppTheme.spacingMedium),
            _buildTitleField(),
            SizedBox(height: AppTheme.spacingMedium),

            // 内容区域
            _buildSectionHeader('卡片内容', Icons.content_paste),
            SizedBox(height: AppTheme.spacingMedium),
            _buildQuestionField(),
            SizedBox(height: AppTheme.spacingMedium),
            _buildAnswerField(),

            // 底部间距
            SizedBox(height: 100.h), // 为底部按钮留出空间
          ],
        ),
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActions() {
    return Container(
      padding: EdgeInsets.all(AppTheme.paddingLarge.left),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Obx(() {
          return ElevatedButton.icon(
            onPressed: _controller.isSaving ? null : _saveCard,
            icon: _controller.isSaving
                ? SizedBox(
                    width: 16.w,
                    height: 16.h,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Icon(Icons.save),
            label: OxText(_controller.isSaving ? '保存中...' : '保存更改'),
            style: ElevatedButton.styleFrom(
              minimumSize: Size(double.infinity, AppTheme.buttonHeight),
              textStyle: TextStyle(fontSize: AppTheme.fontTitle),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              ),
            ),
          );
        }),
      ),
    );
  }

  /// 构建卡片预览区域
  Widget _buildCardPreview() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(AppTheme.paddingMedium.left),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.preview,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20.w,
                ),
                SizedBox(width: 8.w),
                Text(
                  '卡片预览',
                  style: TextStyle(
                    fontSize: AppTheme.fontTitle,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            SizedBox(height: AppTheme.spacingMedium),
            Obx(() {
              if (_controller.titleText.isNotEmpty) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _controller.titleText,
                      style: TextStyle(
                        fontSize: AppTheme.fontLarge,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),
            Obx(() {
              if (_controller.questionText.isNotEmpty) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '问题: ${_controller.questionText}',
                      style: TextStyle(fontSize: AppTheme.fontBody),
                    ),
                    SizedBox(height: 4.h),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),
            Obx(() {
              if (_controller.answerText.isNotEmpty) {
                return Text(
                  '答案: ${_controller.answerText}',
                  style: TextStyle(fontSize: AppTheme.fontBody),
                );
              }
              return const SizedBox.shrink();
            }),
            Obx(() {
              if (_controller.titleText.isEmpty &&
                  _controller.questionText.isEmpty &&
                  _controller.answerText.isEmpty) {
                return Text(
                  '开始输入内容来预览卡片...',
                  style: TextStyle(
                    fontSize: AppTheme.fontBody,
                    color:
                        Theme.of(context).colorScheme.onSurface.withAlpha(153),
                    fontStyle: FontStyle.italic,
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      ),
    );
  }

  /// 构建区域标题
  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 20.w,
        ),
        SizedBox(width: 8.w),
        Text(
          title,
          style: TextStyle(
            fontSize: AppTheme.fontTitle,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  /// 构建标题输入框
  Widget _buildTitleField() {
    return _buildEnhancedTextFormField(
      controller: _controller.titleController,
      labelText: '标题',
      hintText: '输入卡片标题',
      icon: Icons.title,
      validator: _controller.validateTitle,
      maxLength: 100,
      isRequired: true,
    );
  }

  /// 构建问题输入框
  Widget _buildQuestionField() {
    return _buildEnhancedTextFormField(
      controller: _controller.questionController,
      labelText: '问题',
      hintText: '输入问题（可选）',
      icon: Icons.help_outline,
      maxLines: 5,
      maxLength: 1000,
      validator: _controller.validateQuestion,
    );
  }

  /// 构建答案输入框
  Widget _buildAnswerField() {
    return _buildEnhancedTextFormField(
      controller: _controller.answerController,
      labelText: '答案',
      hintText: '输入答案（可选）',
      icon: Icons.lightbulb_outline,
      maxLines: 5,
      maxLength: 1000,
      validator: _controller.validateAnswer,
    );
  }

  /// 构建增强的文本输入框
  Widget _buildEnhancedTextFormField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required IconData icon,
    int maxLines = 1,
    int? maxLength,
    bool isRequired = false,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            labelText: labelText + (isRequired ? ' *' : ''),
            hintText: hintText,
            prefixIcon:
                Icon(icon, color: Theme.of(context).colorScheme.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.outline.withAlpha(128),
                width: 1.5,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 2.5,
              ),
            ),
            filled: true,
            fillColor: Theme.of(context)
                .colorScheme
                .surfaceContainerHighest
                .withAlpha(77),
            counterText: maxLength != null ? null : '',
          ),
          maxLines: maxLines,
          maxLength: maxLength,
          style: TextStyle(fontSize: AppTheme.fontBody),
          validator: validator,
          onChanged: (value) {
            // 文本变化会通过监听器自动更新响应式变量
            // 不需要手动调用update()
          },
        ),
        if (maxLength != null) ...[
          SizedBox(height: 4.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (isRequired)
                Text(
                  '* 必填项',
                  style: TextStyle(
                    fontSize: AppTheme.fontSmall,
                    color: Theme.of(context).colorScheme.error,
                  ),
                )
              else
                const SizedBox.shrink(),
              // 使用ValueListenableBuilder来监听TextEditingController的变化
              ValueListenableBuilder<TextEditingValue>(
                valueListenable: controller,
                builder: (context, value, child) {
                  return Text(
                    '${value.text.length}/$maxLength',
                    style: TextStyle(
                      fontSize: AppTheme.fontSmall,
                      color: value.text.length > maxLength * 0.9
                          ? Theme.of(context).colorScheme.error
                          : Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withAlpha(153),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 构建书籍选择器
  Widget _buildBookSelector() {
    return BookSelector(
      selectedBookId: _controller.bookId?.toString(),
      onBookSelected: (book) {
        if (book != null) {
          _controller.bookId = book.id;
          Console.log('选择了书籍: ${book.name} (ID: ${book.id})');
        } else {
          _controller.bookId = null;
          Console.log('取消选择书籍');
        }
      },
      hintText: '请选择要添加到的书籍',
      isRequired: true,
    );
  }
}
