import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';
import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/widgets/index.dart';

/// 书籍选择器组件
/// 用于在创建卡片时选择目标书籍
class BookSelector extends StatefulWidget {
  final String? selectedBookId;
  final Function(BookModel?) onBookSelected;
  final String? hintText;
  final bool isRequired;

  const BookSelector({
    super.key,
    this.selectedBookId,
    required this.onBookSelected,
    this.hintText,
    this.isRequired = false,
  });

  @override
  State<BookSelector> createState() => _BookSelectorState();
}

class _BookSelectorState extends State<BookSelector> {
  late CreationController _controller;
  BookModel? _selectedBook;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<CreationController>();
    _initializeSelectedBook();
    _loadBooksIfNeeded();
  }

  /// 初始化选中的书籍
  void _initializeSelectedBook() {
    if (widget.selectedBookId != null) {
      _selectedBook = _controller.bookList.firstWhereOrNull(
        (book) => book.id == widget.selectedBookId,
      );
    }
  }

  /// 如果书籍列表为空，则加载书籍
  Future<void> _loadBooksIfNeeded() async {
    if (_controller.bookList.isEmpty) {
      setState(() {
        _isLoading = true;
      });

      try {
        await _controller.loadBookList();
      } catch (e) {
        Console.log('加载书籍列表失败: $e');
        ShowToast.fail('加载书籍列表失败');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 显示书籍选择对话框
  Future<void> _showBookSelectionDialog() async {
    final selectedBook = await showDialog<BookModel>(
      context: context,
      builder: (context) => _BookSelectionDialog(
        books: _controller.bookList,
        selectedBook: _selectedBook,
        onRefresh: () => _controller.loadBookList(),
      ),
    );

    if (selectedBook != null) {
      setState(() {
        _selectedBook = selectedBook;
      });
      widget.onBookSelected(selectedBook);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标签
        Row(
          children: [
            Text(
              '选择书籍',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            if (widget.isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.red,
                ),
              ),
          ],
        ),
        SizedBox(height: 8.h),

        // 选择器
        InkWell(
          onTap: _isLoading ? null : _showBookSelectionDialog,
          borderRadius: BorderRadius.circular(8.r),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              border: Border.all(
                color: _selectedBook != null
                    ? AppTheme.primary
                    : Colors.grey.shade300,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(8.r),
              color: Colors.white,
            ),
            child: Row(
              children: [
                Expanded(
                  child: _isLoading
                      ? Row(
                          children: [
                            SizedBox(
                              width: 16.w,
                              height: 16.w,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: AppTheme.primary,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              '加载中...',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        )
                      : Text(
                          _selectedBook?.name ?? widget.hintText ?? '请选择书籍',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: _selectedBook != null
                                ? Colors.black87
                                : Colors.grey.shade600,
                          ),
                        ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.grey.shade600,
                  size: 20.w,
                ),
              ],
            ),
          ),
        ),

        // 错误提示
        if (widget.isRequired && _selectedBook == null)
          Padding(
            padding: EdgeInsets.only(top: 4.h),
            child: Text(
              '请选择一个书籍',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.red,
              ),
            ),
          ),
      ],
    );
  }
}

/// 书籍选择对话框
class _BookSelectionDialog extends StatefulWidget {
  final List<BookModel> books;
  final BookModel? selectedBook;
  final VoidCallback onRefresh;

  const _BookSelectionDialog({
    required this.books,
    this.selectedBook,
    required this.onRefresh,
  });

  @override
  State<_BookSelectionDialog> createState() => _BookSelectionDialogState();
}

class _BookSelectionDialogState extends State<_BookSelectionDialog> {
  String _searchKeyword = '';
  bool _isRefreshing = false;

  List<BookModel> get _filteredBooks {
    if (_searchKeyword.isEmpty) {
      return widget.books;
    }
    return widget.books.where((book) {
      final name = book.name?.toLowerCase() ?? '';
      final brief = book.brief?.toLowerCase() ?? '';
      final keyword = _searchKeyword.toLowerCase();
      return name.contains(keyword) || brief.contains(keyword);
    }).toList();
  }

  Future<void> _handleRefresh() async {
    setState(() {
      _isRefreshing = true;
    });

    try {
      widget.onRefresh();
    } finally {
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Container(
        width: 320.w,
        height: 500.h,
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // 标题栏
            Row(
              children: [
                Text(
                  '选择书籍',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _isRefreshing ? null : _handleRefresh,
                  icon: _isRefreshing
                      ? SizedBox(
                          width: 16.w,
                          height: 16.w,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: AppTheme.primary,
                          ),
                        )
                      : Icon(
                          Icons.refresh,
                          size: 20.w,
                        ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    size: 20.w,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // 搜索框
            TextField(
              onChanged: (value) {
                setState(() {
                  _searchKeyword = value;
                });
              },
              decoration: InputDecoration(
                hintText: '搜索书籍...',
                prefixIcon: Icon(Icons.search, size: 20.w),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
              ),
            ),
            SizedBox(height: 16.h),

            // 书籍列表
            Expanded(
              child: _filteredBooks.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.book_outlined,
                            size: 48.w,
                            color: Colors.grey.shade600,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            _searchKeyword.isEmpty
                                ? '暂无书籍\n请先创建一本书籍'
                                : '未找到匹配的书籍',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _filteredBooks.length,
                      itemBuilder: (context, index) {
                        final book = _filteredBooks[index];
                        final isSelected = book.id == widget.selectedBook?.id;

                        return ListTile(
                          leading: CircleAvatar(
                            radius: 20.r,
                            backgroundColor: isSelected
                                ? AppTheme.primary
                                : Colors.grey.shade200,
                            child: Icon(
                              Icons.book,
                              color: isSelected
                                  ? Colors.white
                                  : Colors.grey.shade600,
                              size: 20.w,
                            ),
                          ),
                          title: Text(
                            book.name ?? '未命名书籍',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                              color: isSelected
                                  ? AppTheme.primary
                                  : Colors.black87,
                            ),
                          ),
                          subtitle: book.brief?.isNotEmpty == true
                              ? Text(
                                  book.brief!,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.grey.shade600,
                                  ),
                                )
                              : null,
                          trailing: isSelected
                              ? Icon(
                                  Icons.check_circle,
                                  color: AppTheme.primary,
                                  size: 20.w,
                                )
                              : null,
                          onTap: () {
                            Navigator.of(context).pop(book);
                          },
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
