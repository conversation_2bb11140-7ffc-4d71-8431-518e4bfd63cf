part of utils;

/// ShortUUID工具类
/// 用于生成短UUID，与后端保持一致
/// 使用base58编码来生成短UUID
class ShortUuidUtils {
  static const String _alphabet = '23456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz';
  static final _uuid = Uuid();

  /// 生成短UUID
  /// 返回22位长度的短UUID字符串
  static String generate() {
    // 生成标准UUID
    final uuid = _uuid.v4();
    // 转换为短UUID
    return _encodeUuid(uuid);
  }

  /// 验证是否为有效的短UUID格式
  static bool isValid(String? uuid) {
    if (uuid == null || uuid.isEmpty) {
      return false;
    }

    // 短UUID通常是22位长度，包含base58字符
    final regex = RegExp(r'^[23456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz]{20,25}$');
    return regex.hasMatch(uuid);
  }

  /// 从标准UUID转换为短UUID
  static String fromUuid(String uuid) {
    return _encodeUuid(uuid);
  }

  /// 将短UUID转换为标准UUID
  static String toUuid(String shortUuid) {
    return _decodeUuid(shortUuid);
  }

  /// 生成带前缀的短UUID
  /// 例如：book_ABC123DEF456
  static String generateWithPrefix(String prefix) {
    return '${prefix}_${generate()}';
  }

  /// 批量生成短UUID
  static List<String> generateBatch(int count) {
    return List.generate(count, (index) => generate());
  }

  /// 将UUID编码为短UUID
  static String _encodeUuid(String uuid) {
    // 移除连字符
    final cleanUuid = uuid.replaceAll('-', '');

    // 将16进制字符串转换为BigInt
    final bigInt = BigInt.parse(cleanUuid, radix: 16);

    // 使用base58编码
    return _encodeBase58(bigInt);
  }

  /// 将短UUID解码为标准UUID
  static String _decodeUuid(String shortUuid) {
    // 解码base58
    final bigInt = _decodeBase58(shortUuid);

    // 转换为16进制字符串
    var hex = bigInt.toRadixString(16).padLeft(32, '0');

    // 添加连字符
    return '${hex.substring(0, 8)}-${hex.substring(8, 12)}-${hex.substring(12, 16)}-${hex.substring(16, 20)}-${hex.substring(20, 32)}';
  }

  /// Base58编码
  static String _encodeBase58(BigInt number) {
    if (number == BigInt.zero) return _alphabet[0];

    String result = '';
    while (number > BigInt.zero) {
      final remainder = number % BigInt.from(_alphabet.length);
      result = _alphabet[remainder.toInt()] + result;
      number = number ~/ BigInt.from(_alphabet.length);
    }

    return result;
  }

  /// Base58解码
  static BigInt _decodeBase58(String encoded) {
    BigInt result = BigInt.zero;
    final base = BigInt.from(_alphabet.length);

    for (int i = 0; i < encoded.length; i++) {
      final char = encoded[i];
      final index = _alphabet.indexOf(char);
      if (index == -1) {
        throw ArgumentError('Invalid character in base58 string: $char');
      }
      result = result * base + BigInt.from(index);
    }

    return result;
  }
}
