import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/features/creation/pages/card_list_page.dart';
import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/global.dart';

/// 测试卡片列表页面
/// 验证保存的卡片是否能正确显示
void main() {
  runApp(TestCardListApp());
}

class TestCardListApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: '卡片列表测试',
      home: TestCardListPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class TestCardListPage extends StatefulWidget {
  @override
  State<TestCardListPage> createState() => _TestCardListPageState();
}

class _TestCardListPageState extends State<TestCardListPage> {
  late CreationController _controller;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// 初始化服务
  Future<void> _initializeServices() async {
    try {
      // 初始化应用服务
      await Global().init();

      // 等待服务注册完成
      await Future.delayed(const Duration(milliseconds: 500));

      // 初始化控制器
      if (!Get.isRegistered<CreationController>()) {
        Get.put<CreationController>(CreationController());
      }
      _controller = Get.find<CreationController>();

      Console.log('✅ 服务初始化完成');

      // 加载卡片列表
      await _loadCardList();

    } catch (e) {
      Console.log('❌ 服务初始化失败: $e');
    }
  }

  /// 加载卡片列表
  Future<void> _loadCardList() async {
    try {
      Console.log('🔄 开始加载卡片列表...');
      await _controller.loadCardList();
      Console.log('✅ 卡片列表加载完成，数量: ${_controller.cardList.length}');
      
      // 打印卡片信息
      for (int i = 0; i < _controller.cardList.length; i++) {
        final card = _controller.cardList[i];
        Console.log('📝 卡片 ${i + 1}: ${card.title} - ${card.question}');
      }
      
    } catch (e) {
      Console.log('❌ 加载卡片列表失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('卡片列表测试'),
        centerTitle: true,
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 测试信息
            Container(
              width: double.infinity,
              margin: EdgeInsets.all(16),
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🧪 卡片列表测试',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '这个页面用于测试卡片列表功能是否正常工作',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue.shade700,
                    ),
                  ),
                  SizedBox(height: 16),
                  Row(
                    children: [
                      ElevatedButton(
                        onPressed: _loadCardList,
                        child: Text('🔄 重新加载'),
                      ),
                      SizedBox(width: 12),
                      ElevatedButton(
                        onPressed: () {
                          Get.to(() => CardListPage());
                        },
                        child: Text('📋 查看卡片列表'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // 卡片数量统计
            GetBuilder<CreationController>(
              builder: (ctrl) {
                return Container(
                  width: double.infinity,
                  margin: EdgeInsets.symmetric(horizontal: 16),
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '📊 卡片统计',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade800,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '总卡片数量: ${ctrl.cardList.length}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.green.shade700,
                        ),
                      ),
                      if (ctrl.cardList.isNotEmpty) ...[
                        SizedBox(height: 8),
                        Text(
                          '最新卡片: ${ctrl.cardList.first.title}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              },
            ),
            
            // 卡片列表预览
            Expanded(
              child: GetBuilder<CreationController>(
                builder: (ctrl) {
                  if (ctrl.cardList.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.credit_card_outlined,
                            size: 64,
                            color: Colors.grey.shade400,
                          ),
                          SizedBox(height: 16),
                          Text(
                            '暂无卡片',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            '请先创建一些卡片',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade500,
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                  
                  return ListView.builder(
                    padding: EdgeInsets.all(16),
                    itemCount: ctrl.cardList.length,
                    itemBuilder: (context, index) {
                      final card = ctrl.cardList[index];
                      return Card(
                        margin: EdgeInsets.only(bottom: 12),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: Colors.blue,
                            child: Text(
                              '${index + 1}',
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                          title: Text(
                            card.title ?? '未命名卡片',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (card.question?.isNotEmpty == true)
                                Text('问题: ${card.question}'),
                              if (card.answer?.isNotEmpty == true)
                                Text('答案: ${card.answer}'),
                            ],
                          ),
                          trailing: Icon(Icons.arrow_forward_ios),
                          onTap: () {
                            // 显示卡片详情
                            _showCardDetail(card);
                          },
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示卡片详情
  void _showCardDetail(card) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(card.title ?? '卡片详情'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (card.question?.isNotEmpty == true) ...[
              Text('问题:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text(card.question),
              SizedBox(height: 12),
            ],
            if (card.answer?.isNotEmpty == true) ...[
              Text('答案:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text(card.answer),
              SizedBox(height: 12),
            ],
            Text('ID: ${card.id}'),
            Text('类型: ${card.type ?? 'general'}'),
            if (card.createdAt != null)
              Text('创建时间: ${card.createdAt}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }
}
