part of routes;

abstract class AppRoutes {
  static final RouteObserver<Route> observer = RouteObservers();
  static List<String> history = [];

  static const splash = '/splash';
  static const application = '/';
  static const cardSearch = '/card_search';
  static const bookSearch = '/book_search';
  static const login = '/auth/login';
  static const auth = '/auth';
  static const register = '/auth/register';
  static const verificationCode = '/verification-code';
  static const userAgreement = '/auth/userAgreement';
  static const forgot = '/auth/forgot';
  static const my = '/my';
  static const profileEdit = '/profile_edit';
  static const profileEditNew = '/profile/edit';
  static const imageCrop = '/image_crop';
  static const bookInfo = '/book_info';
  static const bookSchedule = '/book_schedule';
  static const review = '/review';
  static const uploadImage = '/upload_image';
  static const uploadAudio = '/upload_audio';
  static const uploadVideo = '/upload_video';
  static const bookEditor = '/book_editor';
  static const cardEditor = '/card_editor'; // 卡片编辑页面
  static const cardEditList = '/card_edit_list';
  static const feedback = '/feedback';
  static const agreement = '/agreement';
  static const aboutUs = '/about_us';
  static const newTry = '/new_try';

  /// Auth模块路由
  static const passwordLogin = '/password_login';


  /// Creation模块路由
  static const creationBookList = '/creation/books';
  static const creationBookEdit = '/creation/books/edit';
  static const creationBookDetail = '/creation/books/detail';
  static const creationCardList = '/creation/cards';
  static const creationCardEdit = '/creation/cards/edit';
  static const creationCardDetail = '/creation/cards/detail';


  /// 通用的页面
  static const imageUpload = '/image_upload';

  /// preload page, deal with preload data
  static const preload = '/preload';

  /// library page
  static String bookshelf = "/bookshelf";

  static final List<GetPage> pages = [
    GetPage(
      name: splash,
      // page: () => const CustomSplash(),
      page: () => const SplashPage(),
    ),
    GetPage(
      name: application,
      transitionDuration: Duration.zero,
      page: () => const ApplicationPage(),
    ),
    GetPage(
      name: my,
      page: () => const ProfilePage(),
    ),
    GetPage(
      name: profileEditNew,
      page: () => const ProfileEditPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: imageCrop,
      page: () => const ImageCropPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),



    /// 图片上传页面
    GetPage(
      name: imageUpload,
      page: () => const ImageUploadPage(),
    ),
    // cardSearch page
    GetPage(
      name: cardSearch,
      page: () => const CardSearchPage(),
    ),

    // review page
    GetPage(
      name: review,
      page: () => const ReviewPage(),
    ),
    // card_editor
    GetPage(
      name: cardEditor,
      page: () => const CardEditorPage(),
    ),

    GetPage(
      name: uploadImage,
      page: () => const UploadImagePage(),
    ),
    GetPage(
      name: uploadAudio,
      page: () => const UploadAudioPage(),
    ),
    GetPage(
      name: uploadVideo,
      page: () => const UploadVideoPage(),
    ),

    /// preload page
    GetPage(
      name: preload,
      page: () => const PreloadPage(),
    ),

    /// bookselft page
    GetPage(
      name: bookshelf,
      page: () => const BookshelfPage(),
    ),

    /// book editor
    GetPage(
      name: bookEditor,
      page: () => const BookEditPage(),
    ),

    /// editing book select
    GetPage(
      name: cardEditList,
      page: () => const CardEditListPage(),
    ),

    GetPage(
      name: feedback,
      page: () => const FeedbackPage(),
    ),

    GetPage(
      name: bookSchedule,
      page: () => const BookSchedulePage(),
    ),

    GetPage(
      name: aboutUs,
      page: () => const AboutUsPage(),
    ),
    GetPage(
      name: newTry,
      page: () => const NewTryPage(),
    ),

    // Creation模块路由
    GetPage(
      name: creationBookList,
      page: () => const BookListPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: creationBookEdit,
      page: () => const BookEditPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: creationBookDetail,
      page: () => const BookDetailPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: creationCardList,
      page: () => const CardListPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: creationCardEdit,
      page: () {
        final arguments = Get.arguments;
        if (arguments is Map<String, dynamic> &&
            arguments['card'] is CardModel) {
          return CardEditPage(card: arguments['card'] as CardModel);
        } else if (arguments is CardModel) {
          return CardEditPage(card: arguments);
        } else {
          // 创建新卡片的情况
          return CardEditPage(card: CardModel());
        }
      },
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 认证相关路由
    GetPage(
      name: auth,
      page: () => const AuthPage(),
      binding: AuthBinding(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    // 密码登录路由
    GetPage(
      name: passwordLogin,
      page: () => const PasswordLoginPage(),
    ),
    GetPage(
      name: login,
      page: () => const AuthPage(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: register,
      page: () => const AuthPage(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: verificationCode,
      page: () => const VerificationCodePage(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 账户安全相关路由 (已合并到profile模块)
    // GetPage(
    //   name: security,
    //   page: () => const SecurityPage(),
    //   transition: Transition.rightToLeft,
    //   transitionDuration: const Duration(milliseconds: 300),
    // ),
  ];
}


