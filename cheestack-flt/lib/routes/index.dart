library routes;

import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/features/auth/pages/auth_page.dart';
import 'package:cheestack_flt/features/auth/pages/password_login_page.dart';
import 'package:cheestack_flt/features/auth/pages/verification_code_page.dart';
import 'package:cheestack_flt/features/creation/pages/card_edit_page.dart';
import 'package:cheestack_flt/shared/widgets/upload_audio/index.dart';
import 'package:cheestack_flt/shared/widgets/upload_image/index.dart';
import 'package:cheestack_flt/shared/widgets/upload_video/index.dart';
import 'package:cheestack_flt/pages/new_try/index.dart';
import 'package:cheestack_flt/pages/book_schedule/index.dart';
import 'package:cheestack_flt/pages/bookshelf/index.dart';
import 'package:cheestack_flt/pages/card_editor/index.dart';
import 'package:cheestack_flt/pages/card_search/index.dart';
import 'package:cheestack_flt/pages/card_edit_list/index.dart';
import 'package:cheestack_flt/pages/feedback/index.dart';
import 'package:cheestack_flt/pages/image_upload/index.dart';
import 'package:cheestack_flt/features/application/index.dart';
import 'package:cheestack_flt/pages/preload/index.dart';
import 'package:cheestack_flt/features/profile/index.dart';
import 'package:cheestack_flt/features/creation/index.dart';
import 'package:cheestack_flt/pages/review/index.dart';
import 'package:cheestack_flt/pages/splash/index.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

part 'middlewares.dart';
part 'observers.dart';
part 'routes.dart';
part 'transitions.dart';
