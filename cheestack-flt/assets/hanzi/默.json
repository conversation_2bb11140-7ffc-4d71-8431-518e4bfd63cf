{"strokes": ["M 183 700 Q 171 704 145 708 Q 133 711 131 705 Q 125 699 134 684 Q 167 618 189 509 Q 193 476 212 454 Q 230 435 234 449 Q 237 459 237 471 L 235 496 Q 234 509 231 522 Q 204 639 199 672 C 195 697 195 697 183 700 Z", "M 428 513 Q 444 485 461 478 Q 471 471 485 490 Q 498 515 527 648 Q 534 676 556 699 Q 568 709 557 723 Q 542 738 504 761 Q 483 770 403 747 Q 399 747 272 720 Q 223 708 183 700 C 154 694 170 663 199 672 Q 239 685 296 698 L 332 705 Q 452 735 469 719 Q 482 704 479 690 Q 458 566 448 546 Q 444 536 430 532 C 420 526 420 526 428 513 Z", "M 232 618 Q 256 561 272 554 Q 279 551 287 560 Q 290 567 288 580 Q 285 593 271 606 Q 250 627 239 632 Q 235 635 232 628 Q 229 624 232 618 Z", "M 397 639 Q 390 615 370 576 Q 367 572 371 568 Q 375 567 380 571 Q 423 617 444 636 Q 454 642 450 648 Q 444 657 430 665 Q 417 674 402 672 Q 393 669 397 658 Q 400 649 397 639 Z", "M 351 497 Q 390 506 428 513 C 453 518 453 518 430 532 Q 409 548 358 532 Q 354 531 352 530 L 314 518 Q 271 508 235 496 C 206 487 208 462 237 471 Q 246 475 314 488 L 351 497 Z", "M 347 317 Q 347 351 348 384 L 349 426 Q 349 463 351 497 L 352 530 Q 353 582 355 628 Q 358 682 349 692 Q 340 701 332 705 C 307 722 282 725 296 698 Q 302 686 308 676 Q 315 652 314 518 L 314 488 Q 313 455 312 413 L 312 374 Q 311 346 311 310 C 311 280 347 287 347 317 Z", "M 348 384 Q 373 393 404 399 Q 429 406 432 409 Q 439 416 435 423 Q 428 432 405 437 Q 389 438 349 426 L 312 413 Q 305 412 299 409 Q 265 400 225 394 Q 197 388 218 376 Q 251 358 299 372 Q 305 375 312 374 L 348 384 Z", "M 311 310 Q 176 286 149 287 Q 137 287 135 279 Q 132 267 140 260 Q 159 244 185 225 Q 194 221 205 228 Q 245 252 290 266 Q 357 287 424 309 Q 449 318 469 329 Q 481 335 482 342 Q 478 348 467 346 Q 410 333 347 317 L 311 310 Z", "M 130 177 Q 115 125 91 75 Q 76 42 96 15 Q 105 -1 123 13 Q 162 53 160 127 Q 161 158 151 179 Q 147 186 140 187 Q 133 186 130 177 Z", "M 234 165 Q 246 111 260 92 Q 269 85 281 90 Q 290 97 294 109 Q 300 142 257 186 Q 247 196 240 197 Q 236 198 232 189 Q 228 180 234 165 Z", "M 334 202 Q 364 142 382 137 Q 391 134 399 146 Q 402 155 398 171 Q 394 186 377 196 Q 353 215 341 221 Q 335 224 332 215 Q 331 211 334 202 Z", "M 428 228 Q 468 174 476 173 Q 483 172 490 181 Q 494 191 493 213 Q 492 228 473 241 Q 427 269 416 266 Q 412 265 411 255 Q 412 246 428 228 Z", "M 656 400 Q 717 416 783 431 Q 826 443 832 449 Q 839 456 835 465 Q 828 475 800 482 Q 770 486 742 474 Q 702 459 661 446 L 610 433 Q 565 423 516 416 Q 480 409 506 394 Q 546 373 601 387 Q 602 388 604 387 L 656 400 Z", "M 654 392 Q 655 396 656 400 L 661 446 Q 664 485 671 628 Q 672 682 691 745 Q 695 755 687 763 Q 669 779 635 792 Q 614 801 599 795 Q 580 788 595 771 Q 623 743 623 714 Q 624 554 610 433 L 604 387 Q 579 215 500 128 Q 466 91 392 40 Q 380 34 378 30 Q 374 23 389 23 Q 401 23 448 44 Q 482 59 531 98 Q 615 170 648 349 L 654 392 Z", "M 648 349 Q 655 339 661 325 Q 736 175 800 69 Q 810 50 835 45 Q 923 36 957 37 Q 972 36 978 42 Q 982 48 974 53 Q 860 120 816 168 Q 753 235 668 375 Q 664 387 654 392 C 632 413 633 375 648 349 Z", "M 767 654 Q 792 632 819 606 Q 832 593 849 594 Q 859 595 865 609 Q 869 624 860 653 Q 854 672 824 685 Q 755 710 740 703 Q 736 699 737 686 Q 740 676 767 654 Z"], "medians": [[[140, 698], [166, 677], [173, 663], [224, 455]], [[190, 698], [213, 691], [287, 713], [342, 718], [417, 739], [456, 743], [493, 735], [515, 709], [490, 599], [463, 515], [440, 526]], [[239, 623], [276, 566]], [[409, 662], [421, 645], [375, 573]], [[238, 477], [257, 490], [422, 528]], [[303, 694], [328, 683], [335, 639], [330, 337], [316, 317]], [[219, 386], [274, 384], [377, 413], [425, 418]], [[147, 275], [193, 259], [413, 318], [474, 342]], [[141, 176], [132, 101], [110, 22]], [[241, 188], [271, 122], [271, 105]], [[341, 211], [378, 168], [385, 151]], [[419, 258], [464, 219], [478, 182]], [[508, 406], [572, 403], [769, 455], [823, 459]], [[601, 782], [622, 774], [654, 744], [638, 459], [615, 307], [571, 188], [529, 126], [488, 88], [433, 50], [384, 28]], [[655, 385], [664, 351], [713, 265], [781, 161], [833, 96], [850, 84], [971, 46]], [[745, 697], [823, 648], [845, 614]]], "radStrokes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}