buildscript {
    ext.kotlin_version = '1.7.10'
    repositories {
        maven { url 'https://dl.bintray.com/kotlin/kotlin-eap' }
        maven { url 'https://dl.bintray.com/kotlin/kotlin-dev' }
        maven { url "https://kotlin.bintray.com/kotlinx" }
        
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        mavenCentral()
    }
    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.android.tools.build:gradle:7.3.0'
    }
}

allprojects {
    repositories {
        jcenter()
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        mavenCentral()
    }
    configurations.all {
        resolutionStrategy {
            force 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4'
            force 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
            force 'org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4'
        }
    }
}

subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin('com.android.library') || project.plugins.hasPlugin('com.android.application')) {
            println "project: ${project.name} Namespace get: ${project.android.namespace}"
            def packageName = project.android.namespace ?: project.android.defaultConfig.applicationId ?: project.android.sourceSets.main.manifest.srcFile.text.find(/package="([^"]*)"/) ?: project.group
            project.android.namespace = packageName
            println "Namespace set to: ${packageName} for project: ${project.name}"
            def manifestFile = project.android.sourceSets.main.manifest.srcFile
            if (manifestFile.exists()) {
                def manifestText = manifestFile.text
                if (manifestText.contains('package=')) {
                    manifestText = manifestText.replaceAll(/package="[^"]*"/, "")
                    manifestFile.text = manifestText
                    println "Package attribute removed in AndroidManifest.xml for project: ${project.name}"
                } else {
                    println "No package attribute found in AndroidManifest.xml for project: ${project.name}"
                }
            } else {
                println "AndroidManifest.xml not found for project: ${project.name}"
            }
            android {
                compileSdkVersion 34
                
                defaultConfig {
                    minSdkVersion 21
                    targetSdkVersion 34
                }
            }
        }
    }
}


rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}