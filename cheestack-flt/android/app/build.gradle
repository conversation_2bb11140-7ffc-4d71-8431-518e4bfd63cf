plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('app/key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.chivetech.cheestack"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.chivetech.cheestack"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true  // 启用代码混淆
            shrinkResources true  // 启用资源优化
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

}

flutter {
    source = "../.."
}


repositories {
    flatDir {
        dirs 'libs'
    }
    maven { url 'https://artifact.bytedance.com/repository/AwemeOpenSDK' }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    // 广点通 SDK
    implementation (name: 'GDTSDK.unionNormal.4.563.1433', ext: 'aar')
    // 广点通 Adapter
    implementation(name: 'mediation_gdt_adapter_4.563.1433.0', ext: 'aar')
    // 百度SDK
    implementation(name: 'Baidu_MobAds_SDK_v9.34', ext: 'aar')
    // 百度 Adapter
    implementation(name: 'mediation_baidu_adapter_9.34.0', ext: 'aar')
    // 快手SDK
    implementation(name: 'kssdk-ad-3.3.59', ext: 'aar')
    // 快手 Adapter
    implementation(name: 'mediation_ks_adapter_3.3.59.0', ext: 'aar')
}