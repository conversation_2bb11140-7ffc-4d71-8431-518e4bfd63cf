package com.chivetech.cheestack_flt

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.telephony.TelephonyManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.plugin.common.PluginRegistry

/** NativeMobilePlugin */
class NativeMobilePlugin: FlutterPlugin, MethodCallHandler, ActivityAware, PluginRegistry.RequestPermissionsResultListener {
    private lateinit var channel: MethodChannel
    private var context: Context? = null
    private var activity: android.app.Activity? = null
    private var pendingResult: Result? = null

    companion object {
        private const val CHANNEL_NAME = "com.cheestack.flt/native_mobile"
        private const val PERMISSION_REQUEST_CODE = 1001
        private val REQUIRED_PERMISSIONS = arrayOf(
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.READ_PHONE_NUMBERS
        )
    }

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, CHANNEL_NAME)
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "getNativeMobile" -> {
                getNativeMobile(result)
            }
            "checkPermission" -> {
                val hasPermission = checkPermissions()
                result.success(hasPermission)
            }
            "requestPermission" -> {
                requestPermissions(result)
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun getNativeMobile(result: Result) {
        if (!checkPermissions()) {
            result.error("PERMISSION_DENIED", "需要手机状态权限", null)
            return
        }

        try {
            val telephonyManager = context?.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
            if (telephonyManager == null) {
                result.error("SERVICE_UNAVAILABLE", "无法获取电话服务", null)
                return
            }

            // 尝试获取手机号码
            val phoneNumber = when {
                ActivityCompat.checkSelfPermission(context!!, Manifest.permission.READ_PHONE_NUMBERS) == PackageManager.PERMISSION_GRANTED -> {
                    telephonyManager.line1Number
                }
                ActivityCompat.checkSelfPermission(context!!, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED -> {
                    telephonyManager.line1Number
                }
                else -> null
            }

            if (phoneNumber.isNullOrEmpty()) {
                result.error("PHONE_NUMBER_UNAVAILABLE", "无法获取本机号码，可能SIM卡未设置或运营商不支持", null)
            } else {
                // 格式化手机号码（移除国家代码等）
                val formattedNumber = formatPhoneNumber(phoneNumber)
                result.success(formattedNumber)
            }
        } catch (e: Exception) {
            result.error("UNKNOWN_ERROR", "获取手机号码时发生错误: ${e.message}", null)
        }
    }

    private fun checkPermissions(): Boolean {
        return REQUIRED_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context!!, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    private fun requestPermissions(result: Result) {
        if (activity == null) {
            result.error("ACTIVITY_NOT_AVAILABLE", "Activity不可用", null)
            return
        }

        if (checkPermissions()) {
            result.success(true)
            return
        }

        pendingResult = result
        ActivityCompat.requestPermissions(activity!!, REQUIRED_PERMISSIONS, PERMISSION_REQUEST_CODE)
    }

    private fun formatPhoneNumber(phoneNumber: String): String {
        // 移除非数字字符
        val digitsOnly = phoneNumber.replace(Regex("[^0-9]"), "")
        
        return when {
            // 如果是+86开头的中国号码，移除+86
            digitsOnly.startsWith("86") && digitsOnly.length == 13 -> {
                digitsOnly.substring(2)
            }
            // 如果是11位数字，直接返回
            digitsOnly.length == 11 -> {
                digitsOnly
            }
            // 其他情况返回原始数字
            else -> digitsOnly
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ): Boolean {
        if (requestCode == PERMISSION_REQUEST_CODE) {
            val allGranted = grantResults.all { it == PackageManager.PERMISSION_GRANTED }
            pendingResult?.success(allGranted)
            pendingResult = null
            return true
        }
        return false
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }

    override fun onAttachedToActivity(binding: ActivityPluginBinding) {
        activity = binding.activity
        binding.addRequestPermissionsResultListener(this)
    }

    override fun onDetachedFromActivityForConfigChanges() {
        activity = null
    }

    override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
        activity = binding.activity
        binding.addRequestPermissionsResultListener(this)
    }

    override fun onDetachedFromActivity() {
        activity = null
    }
}
