# Flutter 相关
-keep class io.flutter.** { *; }
-keep class com.example.** { *; }

# 反射相关
-keepattributes *Annotation*
-keepclassmembers class * {
    @Keep <methods>;
}

# 避免混淆主入口
-keep public class * extends android.app.Application
-keep public class * extends io.flutter.app.FlutterApplication

# 保留 Parcelable 序列化类
-keep class * implements android.os.Parcelable { *; }

# 保留 JSON 解析（如Gson、Jackson等）
-keep class com.google.gson.** { *; }
-keep class org.json.** { *; }
