import pytz
from fastapi import FastAPI
from core import exceptions, middleware
from core import events
from core.routers import AppRoutes


# # 初始化app
# # app = create_app()
app = FastAPI()

# 设置当前时区为东八区（北京时间）
timezone = pytz.timezone("Asia/Shanghai")


# 添加路由
app.include_router(AppRoutes)


# 添加中间键
middleware.add_middleware(app)
# 添加异常处理
exceptions.add_exception_handle(app)

# 事件监听
app.add_event_handler("startup", events.startup(app))
app.add_event_handler("shutdown", events.stopping(app))
