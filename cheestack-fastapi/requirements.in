arrow==1.3.0  # 处理日期和时间的库

fastapi==0.111.1  # FastAPI 框架本身
uvicorn[standard]==0.30.3  # FastAPI 服务器
tortoise-orm==0.21.5  # 异步 ORM 框架，用于与数据库交互
asyncpg==0.29.0  # PostgreSQL 异步驱动程序

passlib==1.7.4  # 用于密码哈希和验证

pydantic==2.8.2  # 用于数据验证和配置文件解析
pydantic-settings==2.4.0 # pydantic基础配置文件


PyJWT==2.8.0  # 用于生成和验证 JSON Web Tokens（JWT）
python-dotenv==1.0.1  # 用于加载和解析环境变量

qcloud-python-sts==3.1.6  # 腾讯云 STS（临时安全令牌）SDK

shortuuid==1.0.13  # 用于生成短 UUID
# redis-py-cluster==2.1.3 # 用于与 集群版的Redis 数据库交互
redis==3.5.3 # 用于与 非集群版的Redis 数据库交互
aerich==0.7.2  # Tortoise-ORM 的数据库迁移工具
azure-cognitiveservices-speech==1.41.1 # 微软azure语音SDK
edge-tts==6.1.15  # edge 语音合成 SDK
# 数据相关
alibabacloud_dysmsapi20170525==2.0.24  # 阿里云短信服务 SDK
cos-python-sdk-v5 == 1.9.27 # 腾讯云对象存储 SDK
itsdangerous==2.2.0  # 用于生成和验证安全令牌,腾讯云需要


httpx==0.27.0 # 异步网络请求
pytest==8.3.2 # 测试
pytest-asyncio==0.23.8 # 异步测试

ollama==0.3.3 # 高版本会冲突