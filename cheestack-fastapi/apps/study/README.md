# Study App - 间隔重复学习系统

## 📖 概述

Study App 是 CheeStack 项目的核心学习模块，实现了完整的间隔重复学习系统（Spaced Repetition System, SRS）。支持多种类型的知识卡片、智能学习调度、多媒体资源管理和学习进度跟踪。

### 🎯 核心功能
- **📚 书籍管理**: 将卡片组织成书籍，支持封面上传和隐私设置
- **🃏 多类型卡片**: 支持通用卡片、语言学习、汉字书写、朗读等多种卡片类型
- **🎵 多媒体支持**: 图片、音频、视频等资源的上传和管理
- **🧠 智能学习算法**: 基于艾
宾浩斯遗忘曲线的复习时间计算
- **📊 学习统计**: 详细的学习记录和进度分析
- **👥 社交功能**: 卡片点赞和分享功能


## 📊 数据模型

### 核心实体关系
```
User (用户)
├── Book (书籍) - 一对多
├── Card (卡片) - 一对多  
├── Asset (资源) - 一对多
├── Schedule (学习计划) - 一对多
├── Record (学习记录) - 一对多
└── BookSchedule (书籍学习计划) - 一对多

Book (书籍)
└── Card (卡片) - 多对多 (通过 book_card 中间表)

Card (卡片)
├── Asset (资源) - 多对多 (通过 card_asset 中间表)
├── User (点赞用户) - 多对多 (通过 card_like 中间表)
├── Schedule (学习计划) - 一对多
└── Record (学习记录) - 一对多
```

### 主要数据表

#### 📚 Book (书籍表)
- `user`: 创建用户
- `name`: 书名
- `brief`: 简介
- `cover`: 封面图片URL
- `privacy`: 隐私设置 (private/free/member_free/paid)

#### 🃏 Card (卡片表)
- `user`: 创建用户
- `type`: 卡片类型 (general/language_general/hanzi_writer等)
- `type_version`: 版本号
- `title`: 卡片标题
- `question`: 问题内容
- `answer`: 答案内容
- `extra`: 额外信息 (JSON格式)

#### 📁 Asset (资源表)
- `user`: 上传用户
- `type`: 资源类型 (image/audio/video/file)
- `name`: 资源名称
- `url`: 资源URL

#### 📅 Schedule (学习计划表)
- `user`: 学习用户
- `card`: 关联卡片
- `step`: 学习步骤
- `status`: 学习状态 (learning/finished/skip_finished/pending)
- `next`: 下次复习时间

#### 📝 Record (学习记录表)
- `user`: 学习用户
- `card`: 关联卡片
- `is_correct`: 是否正确
- `file`: 录音文件URL (可选)
- `kickoff`: 是否首次学习

## 🔧 常量定义

### 卡片类型 (CardType)
- `GENERAL`: 通用卡片
- `GENERAL_MARKDOWN`: Markdown格式卡片
- `HANZI_WRITER`: 汉字书写卡片
- `LANGUAGE_GENERAL`: 语言学习卡片
- `LANGUAGE_LISTENING`: 听力练习卡片
- `LANGUAGE_SPEAKING`: 口语练习卡片
- `READALOUD`: 朗读卡片

### 隐私类型 (PrivacyType)
- `PRIVATE`: 私有
- `FREE`: 免费公开
- `MEMBER_FREE`: 会员免费
- `PAID`: 付费

### 学习状态 (StudyStatus)
- `LEARNING`: 学习中
- `FINISHED`: 已完成
- `SKIP_FINISHED`: 跳过完成
- `PENDING`: 待定

### 资源类型 (AssetType)
- `IMAGE`: 图片
- `AUDIO`: 音频
- `VIDEO`: 视频
- `FILE`: 文件

## 🚀 API接口

### 路由前缀
```python
router = APIRouter(prefix="/v1")
```

### 主要接口

#### 📚 书籍管理
- `POST /v1/books` - 创建书籍
- `GET /v1/books` - 获取书籍列表
- `GET /v1/books/{id}` - 获取书籍详情
- `PUT /v1/books/{id}` - 更新书籍
- `DELETE /v1/books/{id}` - 删除书籍

#### 🃏 卡片管理
- `POST /v1/cards` - 创建卡片
- `GET /v1/cards` - 获取卡片列表
- `GET /v1/cards/{id}` - 获取卡片详情
- `PUT /v1/cards/{id}` - 更新卡片
- `DELETE /v1/cards/{id}` - 删除卡片
- `POST /v1/cards/{id}/like` - 点赞/取消点赞卡片

#### 📅 学习管理
- `GET /v1/books/{book_id}/study` - 获取学习内容
- `POST /v1/study/record` - 提交学习记录
- `GET /v1/study/statistics` - 获取学习统计
- `POST /v1/books/{book_id}/schedule` - 设置学习计划
- `GET /v1/books/{book_id}/schedule` - 获取学习计划

#### 📁 资源管理
- `POST /v1/assets` - 上传资源
- `GET /v1/assets` - 获取资源列表
- `DELETE /v1/assets/{id}` - 删除资源

## 🔑 核心功能实现

### 学习算法
```python
def calculateNextReviewTime(step: int, is_correct: bool) -> datetime:
    """
    计算下次复习时间
    基于间隔重复算法，根据学习步骤和正确性调整复习间隔
    """
    # 学习间隔 (分钟)
    intervals = [1, 10, 1440, 4320, 10080]  # 1分钟, 10分钟, 1天, 3天, 7天
    
    if is_correct:
        next_step = min(step + 1, len(intervals))
    else:
        next_step = 1  # 重新开始
    
    interval_minutes = intervals[next_step - 1]
    return datetime.now() + timedelta(minutes=interval_minutes)
```

### 权限检查
```python
async def get_card_queryset(book_id: int, user_id: str):
    """
    检查用户权限，返回可访问的卡片查询集
    """
    book = await Book.get_or_none(id=book_id)
    
    # 根据隐私设置检查权限
    if book.privacy == PrivacyType.FREE or book.user_id == user_id:
        return Card.filter(books__id=book_id)
    elif book.privacy == PrivacyType.PRIVATE:
        raise Sexception(msg="没有权限查看")
    # ... 其他权限检查逻辑
```

### 文件上传
```python
async def uploadFileFromUploadFile(file: UploadFile, location: str = "card"):
    """
    处理文件上传，支持图片、音频等多种格式
    自动上传到腾讯云COS存储
    """
    if not file:
        return None
    
    # 生成唯一文件名
    file_extension = file.filename.split('.')[-1]
    unique_filename = f"{shortuuid.uuid()}.{file_extension}"
    
    # 上传到云存储
    cos_client = Scos()
    file_url = await cos_client.upload_file(file, f"{location}/{unique_filename}")
    
    return file_url
```

## 🔧 依赖和工具函数

### 主要依赖函数 (dependencies.py)
- `get_card_queryset()`: 权限检查和查询集获取
- `uploadFileFromUploadFile()`: 文件上传处理
- `uploadFileFromFile()`: 本地文件上传
- `calculateNextReviewTime()`: 复习时间计算
- `addLikeAndScheduleInfo()`: 添加点赞和学习计划信息

### 外部依赖
- **FastAPI**: Web框架
- **Tortoise ORM**: 异步ORM
- **Pydantic**: 数据验证
- **Arrow/Pytz**: 时间处理
- **httpx**: HTTP客户端
- **shortuuid**: 唯一ID生成

### 内部依赖
- `apps.auth`: 用户认证和权限
- `apps.tcyun`: 腾讯云服务集成
- `core`: 核心功能模块
- `sutils`: 工具函数库

## 🚀 快速开始

### 1. 创建书籍
```python
# POST /v1/books
{
    "name": "英语单词学习",
    "brief": "常用英语单词记忆",
    "privacy": "free"
}
```

### 2. 创建卡片
```python
# POST /v1/cards
{
    "book_id": 1,
    "type": "language_general",
    "title": "Apple",
    "question": "苹果",
    "answer": "Apple",
    "card_assets": [
        {
            "asset_id": 1,
            "type": "primary_image",
            "is_correct": true
        }
    ]
}
```

### 3. 开始学习
```python
# GET /v1/books/1/study
# 返回待学习的卡片

# POST /v1/study/record
{
    "card_id": 1,
    "is_correct": true
}
```

## 📈 性能优化

### 数据库查询优化
- 使用 `prefetch_related()` 预加载关联数据
- 使用 `select_related()` 优化外键查询
- 合理使用索引和唯一约束

### 缓存策略
- 学习统计数据缓存
- 热门书籍和卡片缓存
- 用户学习进度缓存

### 异步处理
- 文件上传异步处理
- 学习记录批量写入
- TTS语音生成异步队列

## 🔒 安全考虑

### 权限控制
- 基于用户身份的资源访问控制
- 书籍隐私设置验证
- 文件上传类型和大小限制

### 数据验证
- Pydantic模型严格验证输入数据
- SQL注入防护
- XSS攻击防护

## 🧪 测试建议

### 单元测试
```python
@pytest.mark.asyncio
async def test_create_card():
    # 测试卡片创建功能
    pass

@pytest.mark.asyncio  
async def test_study_algorithm():
    # 测试学习算法
    pass
```

### 集成测试
- API接口完整性测试
- 数据库事务测试
- 文件上传功能测试

## 🔧 开发注意事项

1. **数据一致性**: 使用事务处理复杂的数据操作
2. **异步编程**: 所有数据库操作都使用async/await
3. **错误处理**: 使用统一的异常处理机制
4. **日志记录**: 关键操作都有详细的日志记录
5. **性能监控**: 注意查询性能和响应时间

## 📝 维护指南

### 添加新卡片类型
1. 在 `consts.py` 中添加新的 `CardType`
2. 更新相关的 Schema 验证
3. 实现对应的处理逻辑
4. 更新前端显示组件

### 扩展学习算法
1. 修改 `calculateNextReviewTime` 函数
2. 调整学习步骤和时间间隔
3. 更新相关测试用例

### 性能优化
1. 定期检查慢查询日志
2. 优化数据库索引
3. 实施缓存策略
4. 监控内存使用情况

## 📋 详细API文档

### 书籍管理API

#### 创建书籍
```http
POST /v1/books
Content-Type: multipart/form-data
Authorization: Bearer {token}

name: "英语学习"
brief: "日常英语单词学习"
privacy: "free"
file: [封面图片文件]
```

**响应示例:**
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "id": 1,
        "name": "英语学习",
        "brief": "日常英语单词学习",
        "cover": "https://cos.example.com/book/cover.jpg",
        "privacy": "free"
    }
}
```

#### 获取书籍列表
```http
GET /v1/books?page=1&size=10&search=英语
Authorization: Bearer {token}
```

#### 获取书籍详情
```http
GET /v1/books/1
Authorization: Bearer {token}
```

### 卡片管理API

#### 创建卡片
```http
POST /v1/cards
Content-Type: application/json
Authorization: Bearer {token}

{
    "book_id": 1,
    "type": "language_general",
    "type_version": 1,
    "title": "Apple - 苹果",
    "question": "What's the English word for 苹果?",
    "answer": "Apple",
    "extra": {
        "pronunciation": "/ˈæpəl/",
        "example": "I eat an apple every day."
    },
    "card_assets": [
        {
            "asset_id": 1,
            "type": "primary_image",
            "text": "红苹果图片",
            "is_correct": true
        },
        {
            "asset_id": 2,
            "type": "primary_audio",
            "text": "Apple发音",
            "is_correct": true
        }
    ]
}
```

#### 更新卡片
```http
PUT /v1/cards/1
Content-Type: application/json
Authorization: Bearer {token}

{
    "title": "Apple - 苹果 (更新)",
    "question": "苹果的英文是什么？",
    "answer": "Apple"
}
```

#### 卡片点赞
```http
POST /v1/cards/1/like
Authorization: Bearer {token}
```

### 学习管理API

#### 获取学习内容
```http
GET /v1/books/1/study?qty=10&study_first=true
Authorization: Bearer {token}
```

**响应示例:**
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "id": 1,
            "type": "language_general",
            "title": "Apple - 苹果",
            "question": "苹果的英文是什么？",
            "answer": "Apple",
            "schedule_id": 1,
            "liked": false,
            "card_assets": [
                {
                    "id": 1,
                    "type": "primary_image",
                    "url": "https://cos.example.com/images/apple.jpg",
                    "is_correct": true
                }
            ]
        }
    ]
}
```

#### 提交学习记录
```http
POST /v1/study/record
Content-Type: application/json
Authorization: Bearer {token}

{
    "card_id": 1,
    "is_correct": true,
    "file": "https://cos.example.com/audio/pronunciation.mp3"
}
```

#### 获取学习统计
```http
GET /v1/study/statistics?date=2024-01-15
Authorization: Bearer {token}
```

**响应示例:**
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "today_new": 5,
        "today_review": 8,
        "today_correct": 11,
        "total_cards": 150,
        "learned_cards": 45,
        "learning_streak": 7
    }
}
```

### 资源管理API

#### 上传资源
```http
POST /v1/assets
Content-Type: multipart/form-data
Authorization: Bearer {token}

type: "image"
name: "苹果图片"
file: [图片文件]
```

## 🎯 使用场景示例

### 场景1: 创建英语单词学习书籍

```python
# 1. 创建书籍
book_data = {
    "name": "托福核心词汇",
    "brief": "托福考试必备单词",
    "privacy": "member_free"
}

# 2. 批量创建单词卡片
words = [
    {"word": "abandon", "meaning": "放弃", "pronunciation": "/əˈbændən/"},
    {"word": "ability", "meaning": "能力", "pronunciation": "/əˈbɪləti/"},
    # ... 更多单词
]

for word in words:
    card_data = {
        "book_id": book_id,
        "type": "language_general",
        "title": f"{word['word']} - {word['meaning']}",
        "question": f"What does '{word['word']}' mean?",
        "answer": word['meaning'],
        "extra": {
            "pronunciation": word['pronunciation'],
            "word_type": "noun"
        }
    }
    # 创建卡片...
```

### 场景2: 汉字书写练习

```python
card_data = {
    "book_id": chinese_book_id,
    "type": "hanzi_writer",
    "title": "学习汉字：学",
    "question": "请写出汉字'学'",
    "answer": "学",
    "extra": {
        "strokes": 8,
        "pinyin": "xué",
        "radical": "子",
        "stroke_order": ["丶", "丶", "冖", "子"]
    }
}
```

### 场景3: 听力练习

```python
card_data = {
    "book_id": listening_book_id,
    "type": "language_listening",
    "title": "听力练习：日常对话",
    "question": "Listen and choose the correct answer",
    "answer": "A",
    "extra": {
        "options": ["A. Good morning", "B. Good evening", "C. Good night"],
        "audio_duration": 3.5
    },
    "card_assets": [
        {
            "asset_id": audio_asset_id,
            "type": "primary_audio",
            "is_correct": true
        }
    ]
}
```

## 🔄 学习流程详解

### 1. 学习调度算法

```python
def get_study_cards(book_id: int, user_id: str, qty: int = 10, study_first: bool = True):
    """
    获取学习卡片的完整流程：

    1. 获取到期的复习卡片
    2. 获取新学习卡片
    3. 根据study_first参数决定优先级
    4. 返回指定数量的卡片
    """

    # 获取到期复习卡片
    review_cards = Schedule.filter(
        user_id=user_id,
        card__books__id=book_id,
        next__lte=datetime.now(),
        status=StudyStatus.LEARNING
    ).limit(qty)

    # 获取新学习卡片
    new_cards = Card.filter(
        books__id=book_id,
        schedules__isnull=True  # 从未学习过的卡片
    ).limit(qty)

    # 根据优先级组合返回
    if study_first:
        return list(new_cards) + list(review_cards)
    else:
        return list(review_cards) + list(new_cards)
```

### 2. 学习记录处理

```python
async def process_study_record(card_id: int, user_id: str, is_correct: bool):
    """
    处理学习记录的完整流程：

    1. 创建学习记录
    2. 更新学习计划
    3. 计算下次复习时间
    4. 更新学习统计
    """

    # 1. 创建学习记录
    record = await Record.create(
        user_id=user_id,
        card_id=card_id,
        is_correct=is_correct,
        kickoff=False  # 根据是否首次学习设置
    )

    # 2. 获取或创建学习计划
    schedule, created = await Schedule.get_or_create(
        user_id=user_id,
        card_id=card_id,
        defaults={"step": 1, "status": StudyStatus.LEARNING}
    )

    # 3. 更新学习计划
    if is_correct:
        schedule.step += 1
        if schedule.step >= 5:  # 完成所有学习步骤
            schedule.status = StudyStatus.FINISHED
    else:
        schedule.step = 1  # 重新开始

    # 4. 计算下次复习时间
    schedule.next = calculateNextReviewTime(schedule.step, is_correct)
    await schedule.save()

    return record
```

## 🛠️ 开发工具和调试

### 数据库查询调试

```python
# 启用SQL查询日志
import logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("tortoise.db_client")
logger.setLevel(logging.DEBUG)

# 查看生成的SQL
cards = await Card.filter(user_id=user_id).prefetch_related("assets")
```

### 性能分析

```python
import time
from functools import wraps

def timing_decorator(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start = time.time()
        result = await func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} took {end - start:.2f} seconds")
        return result
    return wrapper

@timing_decorator
async def get_study_cards_with_timing(book_id, user_id):
    # 你的查询逻辑
    pass
```

### 测试数据生成

```python
async def create_test_data():
    """生成测试数据"""

    # 创建测试用户
    user = await User.create(
        username="test_user",
        mobile="13800138000"
    )

    # 创建测试书籍
    book = await Book.create(
        user=user,
        name="测试书籍",
        brief="用于测试的书籍",
        privacy=PrivacyType.FREE
    )

    # 创建测试卡片
    for i in range(100):
        card = await Card.create(
            user=user,
            type=CardType.GENERAL,
            title=f"测试卡片 {i+1}",
            question=f"问题 {i+1}",
            answer=f"答案 {i+1}"
        )
        await book.cards.add(card)

    print(f"Created test data: 1 user, 1 book, 100 cards")
```

## 🚨 常见问题和解决方案

### 问题1: 学习计划重复创建
**现象**: 同一用户对同一卡片创建了多个学习计划
**解决**: 使用 `unique_together = ("user", "card")` 约束

### 问题2: 文件上传失败
**现象**: 文件上传到云存储失败
**解决**: 检查云存储配置和网络连接，添加重试机制

### 问题3: 学习统计不准确
**现象**: 学习统计数据与实际不符
**解决**: 检查时区设置，确保使用统一的时间标准

### 问题4: 内存使用过高
**现象**: 大量数据查询导致内存占用过高
**解决**: 使用分页查询，避免一次性加载大量数据

## 📚 相关文档

- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Tortoise ORM文档](https://tortoise-orm.readthedocs.io/)
- [Pydantic文档](https://pydantic-docs.helpmanual.io/)
- [CheeStack项目架构文档](../../docs/study-app-architecture-guide.md)

---

📧 如有问题，请联系开发团队或查看项目文档。
