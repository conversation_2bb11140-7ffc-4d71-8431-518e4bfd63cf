from enum import Enum


class CardType(str, Enum):
    GENERAL = "general"
    GENERAL_MARKDOWN = "general_markdown"
    HANZI_WRITER = "hanzi_writer"
    LANGUAGE_GENERAL = "language_general"
    LANGUAGE_LISTENING = "language_listening"
    LANGUAGE_SPEAKING = "language_speaking"
    READALOUD = "read_aloud"


# file type enum
class CardAssetType(str, Enum):
    PRIMARY_IMAGE = "primary_image"
    PRIMARY_AUDIO = "primary_audio"
    SECONDARY_IMAGE = "secondary_image"
    SECONDARY_AUDIO = "secondary_audio"
    OPTION = "option"
    OTHER = "others"


# privacy type
class PrivacyType(str, Enum):
    PRIVATE = "private"
    FREE = "free"
    MEMBER_FREE = "member_free"
    PAID = "paid"


# Schedule Status
class StudyStatus(str, Enum):
    LEARNING = "learning"
    FINISHED = "finished"
    SKIP_FINISHED = "skip_finished"
    PENDING = "pending"


class AssetType(str, Enum):
    IMAGE = "image"
    AUDIO = "audio"
    VEDIO = "vedio"
    FILE = "file"
