from typing import List
from pydantic import BaseModel, Field, model_validator
from tortoise.contrib.pydantic import pydantic_model_creator
from apps.auth.schema import UserPublicSchemaOut
from apps.study.consts import CardAssetType, CardType
from .models import Book, Card
from apps.study.models import Record, Schedule
from apps.tcyun.config import tc_settings

# 使用前先初始化,否则不显示外键, 其中第一个参数为立标,[]中填写的是模型的位置，后面一个参数是app label, 例如 ‘models’
# Tortoise.init_models(TORTOISE_ORM["apps"]["models"]["models"], "models")


BookSchema = pydantic_model_creator(Book, name="BookSchema")
CardSchema = pydantic_model_creator(Card, name="CardSchema")


class CardAssetSchemaIn(BaseModel):
    id: int | None = None
    card_id: int | None = None
    asset_id: int
    type: CardAssetType
    text: str | None = None
    is_correct: bool | None = True


class BookSchemaBase(BaseModel):
    id: int | None = None
    name: str
    brief: str | None = None
    cover: str | None = None
    privacy: str | None = None

    class Config:
        # 从ORM模型转换为Pydantic模型时，使用 from_attributes 来自动转换字段类型
        from_attributes = True


# Book write schema
class BookSchemaIn(BaseModel):
    id: int | None = None
    user_id: str | None = None
    name: str
    brief: str | None = None
    cover: str | None = None
    privacy: str | None = None

    class Config:
        # 从ORM模型转换为Pydantic模型时，使用 from_attributes 来自动转换字段类型
        from_attributes = True

# Book read schema
class BookSchemaOut(BookSchema):
    user: UserPublicSchemaOut | None = None
    name: str
    brief: str | None = None
    cover: str | None = None
    privacy: str

    class Config:
        from_attributes = True


class AssetSchemaOut(BaseModel):
    id: int
    type: str
    name: str
    url: str


# 输入的schema
class CardSchemaIn(BaseModel):
    book_id: int | None = None
    type: str = CardType.GENERAL
    type_version: int = 0
    title: str
    question: str | None = None
    answer: str | None = None
    extra: dict | None = None
    # question_ai_voice: bool | None = False
    # answer_ai_voice: bool | None = False
    card_assets: List[CardAssetSchemaIn] = []

    class Config:
        from_attributes = True
        extra = "ignore"


class CardAssetSchemaOutCheck(BaseModel):
    id: int
    card_id: int
    asset_id: int
    is_correct: bool
    type: CardAssetType
    text: str | None = None
    url: str | None = None
    name: str | None = None
    asset: AssetSchemaOut = Field(None, exclude=True)

    class Config:
        from_attributes = True

    @model_validator(mode="after")
    def get_assets(self):
        self.url = self.asset.url
        self.name = self.asset.name
        return self


# 输出的schema
class CardSchemaOutCheck(BaseModel):
    id: int
    type: str = CardType.GENERAL
    type_version: int = 0
    title: str
    question: str | None = None
    answer: str | None = None
    extra: dict | None = None
    schedule_id: int | None = None
    liked: bool = False
    user: UserPublicSchemaOut | None = None
    card_assets: List[CardAssetSchemaOutCheck] | None = None

    @model_validator(mode="after")
    def add_base_url_to_extra(self) -> "CardSchemaOutCheck":
        if not self.extra:
            return self
        base_url = tc_settings.COS_BASE_URL
        new_extra = {}
        for key, value in self.extra.items():
            if "Url" in key and isinstance(value, str):
                new_extra[key] = base_url + value if not value.startswith("http") else value
            else:
                new_extra[key] = value
        self.extra = new_extra
        return self

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True


class CardAssetSchemaOut(BaseModel):
    id: int
    card_id: int
    asset_id: int
    is_correct: bool
    type: CardAssetType
    text: str | None = None
    url: str | None = None
    name: str | None = None

    class Config:
        from_attributes = True


class CardSchemaOut(CardSchemaOutCheck):
    card_assets: List[CardAssetSchemaOut] | None = None


ScheduleSchema = pydantic_model_creator(Schedule, name="ScheduleSchema")


class ScheduleSchemaOut(ScheduleSchema):
    card: CardSchemaOut = None
    card_id: int | None = None
    user_id: str


class ScheduleSchemaIn(ScheduleSchema):
    id: int | None = None
    card_id: int

    class Config:
        extra = "ignore"


RecordSchema = pydantic_model_creator(Record, name="RecordSchema")


class RecordSchemaOut(RecordSchema):
    card_id: int | None = None
    user_id: str | None = None

    class Config:
        extra = "ignore"


class RecordSchemaIn(RecordSchema):
    id: int | None = None
    card_id: int
    file: str | None = None
    is_correct: bool

    class Config:
        extra = "ignore"


class AssetSchemaIn(BaseModel):
    type: str
    name: str
    url: str


class BookScheduleSchema(BaseModel):
    id: int
    study_qty: int
    study_first: bool = True
    active: bool = True


class BookScheduleSchemaOut(BookScheduleSchema):
    id: int | None
    today_studied: int | None = 0
    study_left: int | None = 0
    review_left: int | None = 0
    total_card: int | None = 0
    total_studied_card: int | None = 0
    user_id: str
    book: BookSchemaBase | None


class BookScheduleSchemaIn(BaseModel):
    book_id: int
    study_qty: int
    study_first: bool
    active: bool = True

    class Config:
        extra = "ignore"
