from collections import Counter
from datetime import datetime, timedelta
from typing import List, Optional
from sutils.url import Surl
from tortoise.transactions import in_transaction
import pytz
from apps.study.consts import  CardType, PrivacyType, CardAssetType, StudyStatus
from apps.study.dependencies import (
    addLikeAndScheduleInfo,
    calculateNextReviewTime,
    uploadFileFromFile,
    uploadFileFromUploadFile,
)
from apps.study.models import Asset, BookSchedule, CardAsset, Record, Schedule
import arrow
from tortoise.functions import Count
from tortoise.expressions import Q

from fastapi import APIRouter, File, Form, Request, Security, UploadFile, status
from apps.auth.deps import check_permissions
from apps.auth.models import Config, User
from apps.study.models import Book, Card
from apps.study.schema import (
    AssetSchemaOut,
    BookScheduleSchemaIn,
    BookScheduleSchemaOut,
    BookSchemaBase,
    BookSchemaOut,
    CardSchemaIn,
    CardSchemaOut,
    CardSchemaOutCheck,
    RecordSchemaIn,
)
from tortoise.expressions import Subquery
from tortoise.query_utils import Prefetch
from apps.tcyun.utils import Scos
from core.exceptions import Sexception
from apps.pronunciation.songs import SongInfo
from core.responses import ApiResponse
from core.schema import BaseResponseModel, resmod
from sutils.models import Smodel
from sutils.filters import Squery
from sutils.log import Slog
from sutils.tts import Stts
from sutils.datetime import Sdatetime
from sutils.string import Sstring


router = APIRouter(prefix="/v1")

@router.post(
    "/cards",
    dependencies=[Security(check_permissions)],
    response_model=BaseResponseModel,
)
async def cards_create(req: Request, data: CardSchemaIn):

    Slog.debug(data.model_dump(exclude_unset=True))
    # 获取添加到的书籍
    book = await Book.get_or_none(id=data.book_id, user=req.state.user).prefetch_related("cards")
    if not book:
        raise Sexception(msg="数据未找到")
    # 排除未设置的数据(包括值为None的数据)
    update_data = data.model_dump(exclude_unset=True, exclude=("card_assets", "book_id"))
    async with in_transaction(connection_name="master"):
        card: Card = await Card.create(user_id=req.state.user_id, **update_data)
        await book.cards.add(card)
        if data.card_assets:
            for item in data.card_assets:
                item.card_id = card.id
                card_asset_data = item.model_dump()
                card_asset_data.pop("id", None)
                await CardAsset.update_or_create(card_id=card.id, type=item.type, defaults=card_asset_data)
    return ApiResponse.success()


@router.put(
    "/cards/{id}",
    dependencies=[Security(check_permissions)],
    response_model=BaseResponseModel,
)
async def cards_update(req: Request, id: int, data: CardSchemaIn):
    # 检查该卡片是否存在且符合权限要求
    obj = await Card.get_or_none(id=id, user=req.state.user)
    if not obj:
        raise Sexception(msg="数据未找到")

    Slog.debug(data)
    # 更新外键信息,使用可取消数据操作
    async with in_transaction(connection_name="master"):
        # 更新卡片信息(不包含外键)
        update_data = data.model_dump(exclude=("id", "book_id"))
        card_assets = update_data.pop("card_assets", [])
        await obj.update_from_dict(update_data).save()
        if card_assets:
            # 获取所有的类型清单
            card_asset_types = [asset_type.value for asset_type in CardAssetType]
            for item in card_assets:
                Slog.debug(item.get("type"))
                # 如果上传数据中包含某个类型的数据,剔除他,这样后面将不会被删除
                try:
                    # card_asset_types.remove(item.get("type"))
                    card_asset_types = [x for x in card_asset_types if x != item.get("type")]
                except Exception as e:
                    Slog.debug(f"{e} - {item.get("type")}")
                # 更新的数据不包含id, 因为id不能被改变
                item.pop("id", None)
                # 更新所有item的card_id为当前卡片
                item["card_id"] = obj.id
                # 如果卡片不存在,则创建他, 否则更新
                card_asset = await CardAsset.get_or_none(type=item["type"], card_id=obj.id)
                if not card_asset:
                    await CardAsset.create(**item)
                else:
                    await CardAsset.update_or_create(
                        type=item["type"],
                        card_id=obj.id,
                        defaults=item,
                    )
            await CardAsset.filter(type__in=card_asset_types, card_id=obj.id).delete()

    return ApiResponse.success(msg="更新成功")




@router.get(
    "/cards/{id}",
    dependencies=[Security(check_permissions)],
    response_model=resmod(CardSchemaOut),
)
async def cards_read(req: Request, id: int):
    # 筛选有访问权限的卡片
    queryset = Card.filter(Q(books__privacy=PrivacyType.FREE) | Q(user=req.state.user))
    obj: Card | None = await queryset.get_or_none(id=id).prefetch_related(
        "user",
        "books",
        "card_assets__asset",
        Prefetch(
            "likes",  # schedules 关系
            queryset=User.filter(id=req.state.user_id), 
            to_attr="filtered_cardlike"  # 通过自定义属性存储
        ),
        Prefetch(
            "schedules",  # schedules 关系
            queryset=Schedule.filter(user_id=req.state.user_id),  # 仅加载特定 user 的 schedules
            to_attr="filtered_schedules"  # 通过自定义属性存储
        )
    )
    if not obj:
        raise Sexception(msg="数据不存在")
    card = addLikeAndScheduleInfo(obj)
    return ApiResponse.success(data=card)


@router.get(
    "/cards",
    dependencies=[Security(check_permissions)],
    response_model=resmod(List[CardSchemaOut]),
)
async def cards_list(
    req: Request,
    skip: int = 0,
    limit: int = 30,
    filters: str = None,
    order: str | None = "-created_at",
):
    # 筛选有访问权限的卡片
    queryset = Card.filter(Q(books__privacy=PrivacyType.FREE) | Q(user=req.state.user))
    # 条件筛选
    if filters:
        query = Squery.dict_to_query(filters=filters)
        queryset = queryset.filter(query)
    # 排序
    order: List[str] = order.split(",") if order else "-created_at"
    objs = await (
        queryset.offset(skip)
        .limit(limit)
        .prefetch_related(
        "user",
        "card_assets__asset",
        Prefetch(
            "likes",  # schedules 关系
            queryset=User.filter(id=req.state.user_id), 
            to_attr="filtered_cardlike"  # 通过自定义属性存储
        ),
        Prefetch(
            "schedules",  # schedules 关系
            queryset=Schedule.filter(user_id=req.state.user_id),  # 仅加载特定 user 的 schedules
            to_attr="filtered_schedules"  # 通过自定义属性存储
        ),)
        .order_by(*order)
        .annotate(studying_count=Count("schedules"))
    )
    if objs:
        cards =[addLikeAndScheduleInfo(obj) for obj in objs ]
        return ApiResponse.success(data=cards)
    else:
        return ApiResponse.success(data=[])


@router.delete(
    "/cards/{id}",
    dependencies=[Security(check_permissions)],
    response_model=BaseResponseModel,
)
async def cards_delete(req: Request, id: int):
    obj = await Card.get_or_none(id=id, user=req.state.user)
    if not obj:
        raise Sexception(msg="未找到或权限不足")
    await obj.delete()
    return ApiResponse.success()


@router.get("/bulkUpdate", dependencies=[Security(check_permissions)])
async def bulkUpdate(req: Request, skip: int = 0, limit: int = 20):
    assets = await Asset.filter(asset_cards__books__id=40, card_assets__type=CardAssetType.SECONDARY_AUDIO)

    for item in assets[1:]:
        card = await Card.get(assets__id=item.id)
        Slog.debug(card.answer)
        tmpPath = "shortuuid.mp3"
        await Stts.tts_async(card.answer, output="shortuuid.mp3")
        key = f"/cheestack/assets/audio/{Sdatetime.unique_time_string()}/{card.answer}.mp3"
        Slog.debug(key)
        Scos.upload(key=key, path=tmpPath)
        Scos.delete(Surl.extract_path(item.url))
        item.url = key
        await item.save()
    return ApiResponse.success()


@router.post(
    "/books",
    dependencies=[Security(check_permissions)],
    response_model=resmod(BookSchemaBase),
)
async def books_create(
    req: Request,
    file: UploadFile | str | None = File(None),  # 期望的字段名是 'file'
    name: str = Form(...),  # 期望的字段名是 'language'
    brief: Optional[str] = Form(None),  # 期望的字段名是 'initial_prompt',
    privacy: Optional[str] = Form(None),  #
):
    # 默认值
    privacy = privacy or PrivacyType.FREE  # 使用 or 简化判断
    # # 排除未设置的数据(包括值为None的数据)
    update_data = {
        "user_id": req.state.user_id,
        "name": name,
        "brief": brief,
        "privacy": privacy,
    }
    file_url = await uploadFileFromUploadFile(file, "book")
    if file_url:
        update_data["cover"] = file_url
    obj = await Book.create(**update_data)
    if not obj:
        raise Sexception(msg="创建失败")

    return ApiResponse.success(data=obj)


@router.put(
    "/books/{id}",
    dependencies=[Security(check_permissions)],
    response_model=BaseResponseModel,
)
async def books_update(
    req: Request,
    id: int,
    file: UploadFile | str | None = File(None),  # 期望的字段名是 'file'
    name: str = Form(...),  # 期望的字段名是 'name'
    brief: Optional[str] = Form(None),  # 期望的字段名是 'brief',
    privacy: Optional[str] = Form(None),  #
):
    obj = await Book.get_or_none(id=id, user_id=req.state.user_id)
    if not obj:
        raise Sexception(msg="数据不存在")
    # 上传文件, 如没有文件会直接返回相应的值
    file_url: str | None = await uploadFileFromUploadFile(file, "book")
    privacy = privacy or PrivacyType.FREE  # 使用 or 简化判断
    # 排除未设置的数据, 同时排除不可以被更新的数据`id`
    update_data = {"name": name, "brief": brief, "privacy": privacy}
    old_url = Surl.extract_path(obj.cover)
    if file_url != old_url:
        update_data["cover"] = file_url
    try:
        obj.update_from_dict(update_data)
        await obj.save()
        Scos.delete(old_url)

    except Exception:
        raise Sexception(msg="更新失败")
    # 更新并直接检查影响行数, 比先查询id再更新更快
    return ApiResponse.success()


# get one books
@router.get("/books/{id}", dependencies=[Security(check_permissions)])
async def books_read(req: Request, id: int):
    obj = await Book.get_or_none(id=id).prefetch_related("user")
    if not obj:
        raise Sexception(msg="data not found")

    return ApiResponse.success(data=Smodel.model_to_dict(obj, BookSchemaOut))


@router.get("/books", dependencies=[Security(check_permissions)])
async def books_list(
    req: Request, skip: int = 0, limit: int = 20, filters: str = None, order: str | None = "-id"
):
    try:
        queryset = Book.filter(Q(user=req.state.user) | Q(privacy=PrivacyType.FREE))
        if filters:
            query = Squery.dict_to_query(filters=filters)
            queryset = queryset.filter(query)
        limit = limit if limit < 50 else 50
        order: List[str] = order.split(",") if order else "-id"
        objs = await queryset.offset(skip).limit(limit).order_by(*order).prefetch_related("user")
        return ApiResponse.success(data=Smodel.models_to_dict(objs, BookSchemaOut))

    except Exception as e:
        Slog.debug(e)
        raise Sexception(msg="未知错误")


# delete one object
@router.delete("/books/{id}", dependencies=[Security(check_permissions)])
async def books_delete(req: Request, id: int):
    obj = await Book.get_or_none(id=id, user=req.state.user)
    if not obj:
        raise Sexception(msg="未找到或权限不足")
    await obj.delete()
    Scos.delete(Surl.extract_path(obj.cover))  # 删除封面图片
    return ApiResponse.success()


# get the listening audio file
@router.get("/listening", dependencies=[Security(check_permissions)])
async def listening_list(req: Request):
    # 筛选一天前复习的内容
    filter_time = (arrow.now().floor("day") - timedelta(days=1)).datetime
    objs = await Record.filter(
        user=req.state.user, created_at__gte=filter_time, card__type=CardType.LANGUAGE_GENERAL
    ).prefetch_related("card__card_assets__asset")
    cards = [item.card for item in objs]
    # cards 根据card.id去重
    cards = list(set(cards))
    song_info_list = []
    for card in cards:
        primary_image = [asset for asset in card.card_assets if asset.type == CardAssetType.PRIMARY_IMAGE]
        primary_audio = [asset for asset in card.card_assets if asset.type == CardAssetType.PRIMARY_AUDIO]
        seconddary_audio = [asset for asset in card.card_assets if asset.type == CardAssetType.SECONDARY_AUDIO]
        # item = Smodel.model_to_dict(card, CardSchemaOut)
        song_info = SongInfo(
            album=primary_image[0].asset.url if len(primary_image) > 0 else "",
            audio_url=primary_audio[0].asset.url if len(primary_audio) > 0 else "",
            title=card.title,
            sub_title=card.question,
        )
        
        secondary_song_info = SongInfo(
            album=primary_image[0].asset.url if len(primary_image) > 0 else "",
            audio_url=seconddary_audio[0].asset.url if len(seconddary_audio) > 0 else "",
            title=card.title,
            sub_title=card.question,
        )
        # 三次primay ,一次secondary
        song_info_list.append(song_info.__dict__)
        song_info_list.append(song_info.__dict__)
        song_info_list.append(song_info.__dict__)
        song_info_list.append(secondary_song_info.__dict__)
    return ApiResponse.success(data=song_info_list)


@router.get("/creation/stats", dependencies=[Security(check_permissions)])
async def creation_stats(req: Request):
    """获取用户创作统计数据"""
    data = {}

    # 获取用户书籍总数
    total_books = await Book.filter(user=req.state.user).count()
    data["total_books"] = total_books

    # 获取用户卡片总数
    total_cards = await Card.filter(user=req.state.user).count()
    data["total_cards"] = total_cards

    # 获取今日和本周创作统计
    today = arrow.now().floor("day")
    week_start = today.shift(days=-today.weekday())

    # 今日创作的书籍数
    today_books = await Book.filter(
        user=req.state.user,
        created_at__gte=today.datetime,
        created_at__lt=today.shift(days=1).datetime,
    ).count()

    # 今日创作的卡片数
    today_cards = await Card.filter(
        user=req.state.user,
        created_at__gte=today.datetime,
        created_at__lt=today.shift(days=1).datetime,
    ).count()

    # 本周创作的书籍数
    week_books = await Book.filter(
        user=req.state.user,
        created_at__gte=week_start.datetime,
    ).count()

    # 本周创作的卡片数
    week_cards = await Card.filter(
        user=req.state.user,
        created_at__gte=week_start.datetime,
    ).count()

    data["today_creations"] = today_books + today_cards
    data["week_creations"] = week_books + week_cards
    data["today_books"] = today_books
    data["today_cards"] = today_cards
    data["week_books"] = week_books
    data["week_cards"] = week_cards

    return ApiResponse.success(data=data)


@router.get("/study/stats", dependencies=[Security(check_permissions)])
async def study_stats(req: Request):
    data = {}
    # annotate统计当前卡片集的卡片数量, cards_count是统计的字段
    book = await Book.get_or_none(book_current_study__user=req.state.user).annotate(
        cards_count=Count("cards")
    )
    # arrow is a better datetime library, floor("day") means to get the start of the day
    today = arrow.now().floor("day")
    # tomorrow equals today + 1 day
    tomorrow = today.shift(days=1)
    # get all records of today
    records = Record.filter(
        user=req.state.user,
        created_at__gte=today.datetime,
        created_at__lt=tomorrow.datetime,
    )

    # 获取当前正在学习的卡片集
    if book:
        data["id"] = book.id
        data["name"] = book.name
        data["cover"] = book.cover
        data["brief"] = book.brief
        data["total"] = book.cards_count

    # gather all records of studying
    in_progress = await Schedule.filter(card__books=book, user=req.state.user).count()
    # return the count of studying
    data["in_progress"] = in_progress if in_progress else 0

    # gather all records of studying
    study_count = await records.filter(kickoff=True).count()
    # gather all records of reviewing
    review_count = await records.filter(kickoff=False).count()
    # return the count of studying
    data["study_count"] = study_count
    # return the count of reviewing
    data["review_count"] = review_count
    # get the count of cards that need to be reviewed
    review_left = await Schedule.filter(
        user=req.state.user, next__lte=datetime.now(pytz.utc), status=StudyStatus.LEARNING
    ).count()
    data["review_left"] = review_left
    return ApiResponse.success(data=data)

@router.get(
    "/to_be_study_cards",
    dependencies=[Security(check_permissions)],
    response_model=resmod(List[CardSchemaOut]),
)
async def study_planned(req: Request, qty: int = 10):
    user_id = req.state.user_id
    # 获取用户配置，使用select_related预加载关联数据
    config = await Config.get_or_none(user_id=user_id)
    if not config or not config.current_study_id:
        raise Sexception(msg="没有可复习的卡片", status_code=status.HTTP_404_NOT_FOUND)
    # 使用最小值来限制卡片数量
    qty = min(qty, config.study_number)
    
    
    # 使用子查询获取已学习的卡片ID
    studied_card_ids = await Schedule.filter(
        user_id=user_id
    ).values_list('card_id', flat=True)
    # 一次性查询所需的所有卡片数据
    cards = await Card.filter(
        Q(books=config.current_study_id) & 
        ~Q(id__in=studied_card_ids)
    ).prefetch_related(
        "card_assets__asset",
        "user",
        Prefetch(
            "likes",
            queryset=User.filter(id=user_id),
            to_attr="filtered_cardlike"
        ),
        Prefetch(
            "schedules",
            queryset=Schedule.filter(user_id=user_id),
            to_attr="filtered_schedules"
        )
    ).limit(qty)
    items = [addLikeAndScheduleInfo(card) for card in cards]
    
    return ApiResponse.success(data=items)
    

@router.get(
    "/study/my_reviews",
    dependencies=[Security(check_permissions)],
    response_model=resmod(List[CardSchemaOut]),
)
async def study_review(req: Request, skip: int = 0, limit: int = 20):
    start_time = datetime.now()
    # 筛选出正在学习的
    objs = (
        await Schedule.filter(
            user=req.state.user,
            next__lte=datetime.now(pytz.utc),
            status=StudyStatus.LEARNING,
        )
        .offset(skip)
        .limit(limit)
        .prefetch_related("card__card_assets__asset", "card__user", "card__likes", "card__schedules")
    )
    items= []
    for schedule in objs:
        instance = CardSchemaOutCheck.model_validate(schedule.card, from_attributes=True)
        card = instance.model_dump(mode="json")
        card["liked"] = 0
        card["schedule_id"] = schedule.id
        items.append(card)
    Slog.debug(f"{datetime.now() - start_time}")
    # return ApiResponse.success(data="cards")
    return ApiResponse.success(data=items)


@router.post(
    "/assets",
    dependencies=[Security(check_permissions)],
    response_model=resmod(AssetSchemaOut),
)
async def assets_create(
    req: Request,
    file: UploadFile | str | None = File(None),
    name: str = Form(...),
    type: str = Form(...),
    ai_voice: bool | None = Form(...),
):
    # 上传文件到cos
    file_url: str | None = await uploadFileFromUploadFile(file, "card")
    
    # 如果没有文件且开启了ai语音
    if not file_url and ai_voice:
        try:
            # 根据`name`生成文件语音文件
            # 判断`word`字符串是中文还是英文
            output = f"{Sstring.replace_punctuation_from_string(name)}.mp3"
            Slog.debug(f"output: {output}")
            if Sstring.detect_language(name) == "en":
                await Stts.tts_async(name, output, rate = "-30%")
            elif Sstring.detect_language(name) == "zh":
                await Stts.tts_async(name, output, rate = "-10%")
            else:
                raise Sexception(data="语言不支持")
            file_url = await uploadFileFromFile(output, "card")
        except Exception as e:
            Slog.debug(e)
            raise Sexception(msg="语音生成失败")
    if not file_url:
        raise Sexception(msg="没有文件")
    
    Slog.debug(f"file_url: {file_url}")

    obj = await Asset.create(user_id=req.state.user_id, url=file_url, name=name, type=type)
    return ApiResponse.success(data=obj)


# delete one object
@router.delete(
    "/assets/{id}",
    response_model=BaseResponseModel,
    dependencies=[Security(check_permissions)],
)
async def assets_delete(req: Request, id: int):
    obj = await Asset.get_or_none(id=id, user=req.state.user)
    if not obj:
        raise Sexception(msg="数据未找到")
    await obj.delete()
    Scos.delete(Surl.extract_path(obj.url))
    return ApiResponse.success()


@router.put(
    "/assets/{id}",
    dependencies=[Security(check_permissions)],
    response_model=resmod(AssetSchemaOut),
)
async def assets_update(
    req: Request,
    id: int,
    file: UploadFile | str | None = File(None),
    name: str = Form(...),
    type: str = Form(...),
):
    obj = await Asset.get_or_none(id=id, user_id=req.state.user_id)
    if not obj:
        raise Sexception(msg="数据不存在")
    file_url: str | None = await uploadFileFromUploadFile(file)
    # 排除未设置的数据, 同时排除不可以被更新的数据`id`
    update_data = {"name": name, "type": type}
    old_url = Surl.extract_path(obj.url)
    if file_url != old_url:
        update_data["url"] = file_url
    try:
        obj.update_from_dict(update_data)
        await obj.save()
        Scos.delete(old_url)
    except Exception:
        Scos.delete(file_url)
        raise Sexception(msg="数据更新失败")
    return ApiResponse.success(data=obj)


@router.get(
    "/assets/{id}",
    dependencies=[Security(check_permissions)],
    response_model=resmod(AssetSchemaOut),
)
async def assets_get(req: Request, id: int):
    obj = await Asset.get_or_none(id=id)
    if not obj:
        raise Sexception(msg="data not found")
    return ApiResponse.success(data=obj)


@router.get(
    "/assets",
    dependencies=[Security(check_permissions)],
)
async def assets_list(
    req: Request, skip: int = 0, limit: int = 20, filters: str = None, order: str | None = "-id"
):
    try:
        queryset = Asset.filter(user_id=req.state.user_id)
        if filters:
            query = Squery.dict_to_query(filters=filters)
            queryset = queryset.filter(query)
        limit = limit if limit < 50 else 50
        order: List[str] = order.split(",") if order else "-created_at"
        objs = await queryset.offset(skip).limit(limit).order_by(*order)
        return ApiResponse.success(data=objs)

    except Exception:
        raise Sexception(msg="未知错误")







@router.post(
    "/study/records",
    dependencies=[Security(check_permissions)],
    response_model=BaseResponseModel,
)
async def records_create(req: Request, data: List[RecordSchemaIn]):
    async with in_transaction(connection_name="master"):
        try:
            for record in data:
                schedule = await Schedule.filter(user=req.state.user, card_id=record.card_id).first()
                if schedule:
                    await Record.create(
                        is_correct=record.is_correct,
                        card_id=record.card_id,
                        user_id=req.state.user_id,
                    )
                    step = schedule.step
                    if record.is_correct:
                        step += 1
                    if step > 9:
                        schedule.status = StudyStatus.FINISHED
                        await schedule.save()
                    else:
                        next = calculateNextReviewTime(step)
                        schedule.step = step
                        schedule.next = next
                        await schedule.save()
                else:
                    await Record.create(
                        is_correct=record.is_correct,
                        card_id=record.card_id,
                        user_id=req.state.user_id,
                        kickoff=True,
                    )
                    next = calculateNextReviewTime(1)
                    await Schedule.create(card_id=record.card_id, user_id=req.state.user_id, next=next)
            return ApiResponse.success(data="操作成功")
        except Exception as e:
            Slog.debug(e)
            raise Sexception(msg="操作失败")



# @router.get("/update_asset_scripts")
# async def update_asset_scripts():
#     assets = await Asset.filter(asset_cards__books__id=40, card_assets__type=CardAssetType.PRIMARY_AUDIO, updated_at__lte="2024-09-03 22:30:06.543188+08").prefetch_related("asset_cards")
    
#     Slog.debug(len(assets))
#     for asset in assets:
#         Slog.debug(asset.id)
#         old_url= Surl.extract_path(asset.url)
#         cards = await asset.asset_cards
#         name = cards[0].question
#         # # 根据`name`生成文件语音文件
#         output = f"{Sstring.replace_punctuation_from_string(name)}.mp3"
#         # 判断`word`字符串是中文还是英文
#         if Sstring.detect_language(name) == "en":
#             await Stts.tts_async(name, output, voice=EdgeVoice.us_AnaNeural, rate = "-15%")
#         elif Sstring.detect_language(name) == "cn":
#             await Stts.tts_async(name, output, voice=EdgeVoice.zh_XiaoxiaoNeural)
#         else:
#             raise Sexception(data="语言不支持")
#         file_url = await uploadFileFromFile(output, "card")
#         asset.url = file_url
#         await asset.save()
#         Scos.delete(old_url)
    
#     return ApiResponse.success()


# get one book_schedules
# @router.get("/book_schedules/{book_id}", dependencies=[Security(check_permissions)])
# async def book_schedules_get(req: Request, id: int):
#     obj = await BookSchedule.get_or_none(id=id)
#     if not obj:
#         return ApiResponse.fail(msg="data not found")
#     return ApiResponse.success(data=obj)


@router.get(
    "/book_schedules",
    dependencies=[Security(check_permissions)],
    response_model=resmod(List[BookScheduleSchemaOut]),
)
async def book_schedule_list(
    req: Request,
    active:bool = False,
    only_unfinished = False,
    book_id:int = None
):
    queryset = BookSchedule.all()
    if book_id:
        queryset = queryset.filter(book__id=book_id)
    if active:
        queryset= queryset.filter(user=req.state.user, active=True)

    book_schedules = (
        await queryset.filter(user=req.state.user)
        .annotate(total_cards=Count('book__cards'))
        .prefetch_related(
            "user",
            "book",
            "book__cards",
            "book__cards__schedules",
        )
    )

    print(f"qty:{len(book_schedules)}")
    objs = []
    if not book_schedules and book_id:
        book = await Book.filter(id=book_id).annotate(cards_count=Count("cards", distinct=True)).first()
        data =[ {
            "id":None,
            "study_qty": 10,
            "study_first": True,
            "book": book,
            "active": True,
            "total_card": book.cards_count,
            "total_studied_card": 0,
            "today_studied": 0,
            "study_left": 0,
            "review_left": 0,
            "user_id": req.state.user_id,
        }]
        return ApiResponse.success(data=data)
    # arrow is a better datetime library, floor("day") means to get the start of the day
    today = arrow.now().floor("day")
    # tomorrow equals today + 1 day
    tomorrow = today.shift(days=1)
    
    # 获取今天本人所有的学习记录
    today_records = await Record.filter(
        user=req.state.user,
        created_at__gte=today.datetime,
        created_at__lt=tomorrow.datetime,
        # kickoff=True,
    ).prefetch_related("card", "card__books")
    
    
    # 获取所有记录的book_id并统计数量
    today_all_records_ids=[]
    for record in today_records:
        for book in record.card.books:
            today_all_records_ids.append(book.id)
    
    # 获取今天开始第一次学习的卡片记录
    today_all_records_ids = []
    today_kickoff_records_ids = []
    for record in today_records:
        for book in record.card.books:
            today_all_records_ids.append(book.id)
            if record.kickoff:
                today_kickoff_records_ids.append(book.id)
            
    today_all_records_count = Counter(today_all_records_ids)
    today_kickoff_records_count = Counter(today_kickoff_records_ids)

    
    # 获取需要复习的所有计划,按书本分类计数
    reviews_book_ids = []
    reviews = await Schedule.filter(user_id=req.state.user_id, next__lte=datetime.now(pytz.utc),status=StudyStatus.LEARNING).prefetch_related("card","card__books") 
    reviews_book_ids = [book.id for review in reviews for book in review.card.books]
    book_schedules_count = Counter(reviews_book_ids)
    

    
    for book_schedule in book_schedules:
        # total_card = await Card.filter(books=book_schedule.book).count()
        total_card = book_schedule.total_cards
        Slog.debug(f"total_card: {total_card}")
        cards = book_schedule.book.cards
        total_studied_card = sum(
            1 for card in cards if any(schedule.user_id == req.state.user_id for schedule in card.schedules)
        )
        # total_studied_card =await Card.filter(books=book_schedule.book, schedules__user_id=req.state.user_id).count()
        
        # 计算今日Kickoff数、学习剩余数、复习剩余数
        book_id = book_schedule.book.id
        today_kickoff = today_kickoff_records_count.get(int(book_id), 0)
        new_study_left = max(book_schedule.study_qty - today_kickoff, 0)
        total_left = total_card - total_studied_card
        new_study_left = min(new_study_left, total_left)

        review_left = book_schedules_count.get(book_id, 0)
        book_today_all_records_count = today_all_records_count.get(int(book_id), 0)
        if total_card==total_studied_card and only_unfinished and review_left==0 and book_today_all_records_count==0:
            continue
        Slog.debug(f"study_left: {new_study_left}, review_left: {review_left}, today_studied: {book_today_all_records_count}")
        schedule = {
            "id":book_schedule.id,
            "study_qty": book_schedule.study_qty,
            "study_first": book_schedule.study_first,
            "book": book_schedule.book,
            "active": book_schedule.active,
            "total_card": total_card,
            "total_studied_card": total_studied_card,
            "today_studied": book_today_all_records_count,
            "study_left": new_study_left,
            "review_left": review_left,
            "user_id": book_schedule.user.id,
        }
        objs.append(schedule)
    return ApiResponse.success(data=objs)


@router.post(
    "/book_schedules",
    dependencies=[Security(check_permissions)],
    response_model=BaseResponseModel,
)
async def book_schedules_create(req: Request, data: BookScheduleSchemaIn):
    obj = await BookSchedule.create(
        **data.model_dump(exclude_unset=True, exclude_defaults=True),
        user_id=req.state.user_id,
    )
    if not obj:
        raise Sexception(msg="创建失败")
    return ApiResponse.success()


# delete one object
@router.delete("/book_schedules/{id}", dependencies=[Security(check_permissions)])
async def book_schedules_delete(req: Request, id: int):
    obj = await BookSchedule.get_or_none(id=id, user=req.state.user)
    if not obj:
        raise Sexception(msg="未找到或权限不足")
    await obj.delete()
    return ApiResponse.success()


# update one
@router.put("/book_schedules/{id}", dependencies=[Security(check_permissions)])
async def book_schedules_update(req: Request,id:int, data: BookScheduleSchemaIn):
    obj = await BookSchedule.get_or_none(id=id)
    if not obj:
        return ApiResponse.error(msg="数据不存在")
    
    Slog.debug(data.model_dump(exclude_unset=True))
    await obj.update_from_dict(
        data.model_dump(exclude_unset=True)
    ).save()
    return ApiResponse.success()


@router.get(
    "/study/studies",
    dependencies=[Security(check_permissions)],
    response_model=resmod(List[CardSchemaOut]),
)
async def study_list(req: Request, book_id:int,):
    
    book_schedule= await BookSchedule.get_or_none(user=req.state.user,book_id=book_id).select_related("book")
    if not book_schedule:
        raise Sexception(msg="数据不存在")
    
    cards =[]
    study_cards = await get_study_cards(req,book_schedule.book.id,book_schedule.study_qty)
    # 将已学习卡片添加到 cards 列表中, 注意这里使用extend把每个元素加入到card,而不是把整个列表添加进去
    review_cards = await get_review_cards(req, book_schedule.book.id)
    
    Slog.debug(f"study_cards: {len(study_cards)}, review_cards: {len(review_cards)}")
    if book_schedule.study_first:
        cards.extend(study_cards)
        cards.extend(review_cards)
    else:
        cards.extend(review_cards)
        cards.extend(study_cards)
    print(len(cards))
    items = [addLikeAndScheduleInfo(card) for card in cards]
    return ApiResponse.success(data=items)
    
    
# 获取需要学习的卡片
async def get_study_cards(req:Request,book_id:int, study_qty:int):
    # # arrow is a better datetime library, floor("day") means to get the start of the day
    today = arrow.now().floor("day")
    # tomorrow equals today + 1 day
    tomorrow = today.shift(days=1)
    # get all records of today
    study_count = await Record.filter(
        user=req.state.user,
        created_at__gte=today.datetime,
        created_at__lt=tomorrow.datetime,
        card__books__id=book_id,
        kickoff=True,
    ).count()
    qty = study_qty - study_count
    if qty<=0:
        return []
    # 子查询，用于获取已学习卡片的 ID 列表
    queryset = Card.filter(Q(books__privacy=PrivacyType.FREE) | Q(user=req.state.user))
    """获取需要学习的卡片"""
    studied_card_ids_subquery = Schedule.filter(user_id=req.state.user_id).values_list("card_id", flat=True)
    # 主查询：通过子查询过滤卡片并预加载关联数据
    cards = await queryset.filter(
        Q(books=book_id) & ~Q(id__in=Subquery(studied_card_ids_subquery))
    ).prefetch_related(
        "card_assets__asset",    # 预加载卡片资产
        "user",                  # 预加载用户信息
        Prefetch(
            "likes",
            queryset=User.filter(id=req.state.user_id),
            to_attr="filtered_cardlike"
        ),
        Prefetch(
            "schedules",
            queryset=Schedule.filter(user_id=req.state.user_id),
            to_attr="filtered_schedules"
        )
    ).limit(qty).order_by("created_at")
    return cards

async def get_review_cards(req: Request, book_id:int,):
    queryset = Card.filter(Q(books__privacy=PrivacyType.FREE) | Q(user=req.state.user))
    
    # 在复习中的卡片
    cards = await queryset.filter(
        books=book_id, schedules__user_id=req.state.user_id, schedules__next__lte=datetime.now(pytz.utc),schedules__status=StudyStatus.LEARNING,
    ).prefetch_related(
        "card_assets__asset",    # 预加载卡片资产
        "user",                  # 预加载用户信息
        Prefetch(
            "likes",
            queryset=User.filter(id=req.state.user_id),
            to_attr="filtered_cardlike"
        ),
        Prefetch(
            "schedules",
            queryset=Schedule.filter(user_id=req.state.user_id),
            to_attr="filtered_schedules"
        )
    )
    
    return cards
