from datetime import datetime
from tortoise import fields, Model
from apps.study.consts import AssetType, CardType, PrivacyType, CardAssetType, StudyStatus
from core.fields import Url<PERSON>dnField
from core.models import TimeStampModelMixin


# from apps.auth.models import User
class Book(TimeStampModelMixin):
    user = fields.ForeignKeyField(
        "models.User",
        related_name="books",
        default=None,
        null=True,
    )
    name = fields.CharField(64, description="书名", default=None, null=True)
    brief = fields.TextField(description="简介", default=None, null=True)
    cover = UrlCdnField(256, description="封面", default=None, null=True)
    # privacy = fields.IntField(description="隐私(1_公开,2_个人,3_VIP)", default=1)
    privacy = fields.CharEnumField(enum_type=PrivacyType, null=False, description="类型")
    # 与`Card`为多对多关系, 允许为空, 通过中间表`book_card`来关联
    cards = fields.ManyToManyField("models.Card", through="book_card", related_name="books")


class BookCard(Model):
    """_summary_

    Args:
        Model (_type_): _description_
    """

    order = fields.IntField(description="卡片在书中的顺序", default=0)
    book = fields.ForeignKeyField(
        "models.Book",
        on_delete=fields.CASCADE,
        related_name="book_card",
    )
    card = fields.ForeignKeyField(
        "models.Card",
        on_delete=fields.CASCADE,
        related_name="book_card",
    )

    class Meta:
        """_summary_"""

        table = "book_card"


class BookSchedule(Model):
    user = fields.ForeignKeyField("models.User", related_name="book_schedules")
    book = fields.ForeignKeyField(
        "models.Book",
        on_delete=fields.CASCADE,
        related_name="book_schedules",
    )
    study_qty = fields.IntField(default=10)
    study_first = fields.BooleanField(default=True)
    active = fields.BooleanField(default=True)

    class Meta:
        table = "book_schedule"
        unique_together = ("user", "book")  # 设置组合唯一约束


class Asset(TimeStampModelMixin):
    user = fields.ForeignKeyField("models.User", related_name="assets")
    type = fields.CharEnumField(enum_type=AssetType, null=False, default=AssetType.FILE, description="类型")
    name = fields.CharField(max_length=255, null=False, description="名称")
    url = UrlCdnField(max_length=255, null=False, description="地址")


class CardAsset(Model):
    type = fields.CharEnumField(enum_type=CardAssetType, null=False, description="类型")
    text = fields.CharField(max_length=255, null=True, description="文本")
    is_correct = fields.BooleanField(default=True, description="是否正确")
    card = fields.ForeignKeyField("models.Card", related_name="card_assets")
    asset = fields.ForeignKeyField("models.Asset", related_name="card_assets")

    class Meta:
        """define the table name and description"""
        table = "card_asset"
        table_description = "卡片与资产中间表"


# 知识卡片模型
class Card(TimeStampModelMixin):
    user = fields.ForeignKeyField("models.User", related_name="cards")
    likes = fields.ManyToManyField("models.User", through="card_like", related_name="liked_cards")
    assets = fields.ManyToManyField("models.Asset", through="card_asset", related_name="asset_cards")
    type = fields.CharEnumField(enum_type=CardType, null=False, default=CardType.GENERAL, description="类型")
    type_version = fields.IntField(null=False, default=1, description="版本号")
    title = fields.CharField(max_length=128, description="卡片描述", default=None, null=True)
    question = fields.TextField(null=True, description="问题")
    answer = fields.TextField(null=True, description="答案")
    extra = fields.JSONField(null=True, description="额外信息")

    class Meta:
        table = "card"
        table_description = "知识卡片"
        # 表名
        table = "card"
        # 表描述
        table_description = "知识卡片"
        # 默认排序
        # ordering = ["-created_at", "id"]


class CardLike(Model):
    card = fields.ForeignKeyField("models.Card", on_delete=fields.CASCADE)
    user = fields.ForeignKeyField("models.User", on_delete=fields.CASCADE)
    class Meta:
        # 表名
        table = "card_like"
        # 表描述
        table_description = "卡片与收藏中间表"


# 学习计划
class Schedule(TimeStampModelMixin):
    user = fields.ForeignKeyField("models.User", related_name="schedules")
    card: fields.ForeignKeyRelation["Card"] = fields.ForeignKeyField(
        "models.Card", related_name="schedules", on_delete=fields.CASCADE
    )
    step = fields.IntField(default=1)
    status = fields.CharEnumField(enum_type=StudyStatus, default=StudyStatus.LEARNING)
    next = fields.DatetimeField(default=datetime.now, null=True, blank=True)

    class Meta:
        table = "schedule"
        table_description = "Study Schedule"
        unique_together = ("user", "card")


# 学习历史记录
class Record(Model):
    user = fields.ForeignKeyField("models.User", related_name="records", on_delete=fields.CASCADE)
    card: fields.ForeignKeyRelation["Card"] = fields.ForeignKeyField(
        "models.Card", related_name="records", on_delete=fields.CASCADE
    )
    created_at = fields.DatetimeField(null=True, auto_now_add=True, description="创建时间")
    is_correct = fields.BooleanField(default=True)
    file = UrlCdnField(max_length=255, null=True, description="地址")
    kickoff = fields.BooleanField(default=False, description="首次学习")  # 主要用于返回当天新学的数量

    class Meta:
        table = "record"
        table_description = "Study record"
