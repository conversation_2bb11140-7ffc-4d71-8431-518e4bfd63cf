import json
import os
from typing import Optional
from fastapi import File, Form, UploadFile
import httpx
import pytz
import shortuuid
from apps.auth.models import User
from apps.study.consts import PrivacyType
from apps.study.models import Book, Card
from apps.study.schema import CardSchemaOutCheck
from apps.tcyun.utils import Scos
from core.exceptions import Sexception
import datetime
from sutils.string import Sstring
from sutils.log import Slog


async def get_card_queryset(book_id: int, user_id: str):
    """
    check the user permission, return all the queryset.
    @param user_id
    @param Book_id
    @return:
    """

    book = await Book.get_or_none(id=book_id)
    user = User.get_or_none(id=user_id)
    if not book or not user:
        raise Sexception(msg="没有合适的数据")

    if book.privacy == PrivacyType.FREE or book.user_id == user_id:
        pass
    elif book.privacy == PrivacyType.PRIVATE:
        raise Sexception(msg="没有权限查看")
    elif book.privacy == PrivacyType.MEMBER_FREE:
        pass
    elif book.privacy == PrivacyType.MEMBER:
        pass
    elif book.privacy == PrivacyType.VIP:
        pass
    # get data
    queryset = Card.filter(book_id=book_id)
    return queryset


async def uploadFileFromUploadFile(file: UploadFile | str | None = None, location: str = "card"):
    if not file:
        return None
    elif isinstance(file, str):
        return file
    else:
        filename = Sstring.replace_punctuation_from_filename(file.filename)
        file_path = shortuuid.uuid()
        with open(file_path, "wb") as f:
            # data = file.file.read()  # 同步方法
            data = await file.read()
            f.write(data)
            f.close()
        try:
            Slog.debug(f"location: {location}")
            key = Scos.get_key(filename, location)
            Slog.debug(key)
            Scos.upload(key=key, path=file_path)
        except Exception as e:
            Slog.debug(e)
            raise Sexception(msg="文件上传失败")
        return key


async def uploadFileFromFile(file_path, location: str = "card"):
    file_name = os.path.basename(file_path)
    key = Scos.get_key(file_name, location)
    Scos.upload(key=key, path=file_path)
    return key


#  计算下次复习时间
def calculateNextReviewTime(step: int):
    init = 0
    time = None
    now = datetime.datetime.now(pytz.utc)
    if step == init + 1:
        time = now + datetime.timedelta(minutes=5)
    elif step == init + 2:
        time = now + datetime.timedelta(minutes=30)
    elif step == init + 3:
        # 比标准时间短1小时
        time = now + datetime.timedelta(hours=10)
        # time = now + datetime.timedelta(hours=11)
    elif step == init + 4:
        # 比标准时间短1小时
        time = now + datetime.timedelta(hours=11)
        # time = now + datetime.timedelta(hours=12)
    elif step == init + 5:
        # 比标准时间短4小时
        time = now + datetime.timedelta(hours=20)
        # time = now + datetime.timedelta(days=1)
    elif step == init + 6:
        # 比标准时间短8小时
        time = now + datetime.timedelta(hours=40)
        # time = now + datetime.timedelta(days=2)
    elif step == init + 7:
        # 比标准时间短8小时
        time = now + datetime.timedelta(hours=64)
        # time = now + datetime.timedelta(days=3)
    elif step >= init + 8:
        # 比标准时间短8小时
        time = now + datetime.timedelta(days=184)
        # time = now + datetime.timedelta(days=8)
    return time

async def getAudioText(
    audio: UploadFile = File(...),
    beam_size: int = Form(...),
    language: str = Form(...),
    initial_prompt: Optional[str] = Form(None),
    hotwords: Optional[str] = Form(None),
):
    try:
        url = "http://127.0.0.1:5001/api/v1/whisper/transcribe"
        files = {"audio": ("audio.wav", audio.file, "audio/mpeg")}
        data = {
            "beam_size": str(beam_size),  # 注意：表单数据应该是字符串
            "language": language,
            "initial_prompt": initial_prompt,
            "hotwords": hotwords,
        }

        async with httpx.AsyncClient() as client:
            response: httpx.Response = await client.post(url, files=files, data=data)

    except Exception as e:
        print(e)
        return "ddd"

    # 返回目标API的响应
    return json.loads(response.text)["data"]


# async def addLikeAndScheduleInfo(req, cards: Card):
#     # 获取所有 card 的 ID 列表
#     card_ids = [card.id for card in cards]
#     user_id = req.state.user_id

#     # 批量查询点赞信息
#     likes_data = await CardLike.filter(card_id__in=card_ids, user_id=user_id).values_list(
#         "card_id", flat=True
#     )

#     # 批量查询 schedule 信息
#     schedules_data = await Schedule.filter(card_id__in=card_ids, user_id=user_id).values_list("card_id", "id")
#     schedules_map = {card_id: schedule_id for card_id, schedule_id in schedules_data}

#     # 构建每个 card 的返回结果
#     result = []
#     for card in cards:
#         # 生成 CardSchemaOutCheck 实例
#         instance = CardSchemaOutCheck.model_validate(card, from_attributes=True)
#         card_data = instance.model_dump(mode="json")
#         # 设置 liked 字段
#         card_data["liked"] = card.id in likes_data
#         # 设置 schedule_id 字段
#         card_data["schedule_id"] = schedules_map.get(card.id)
#         result.append(card_data)

#     return result


def addLikeAndScheduleInfo(obj: Card):
    instance = CardSchemaOutCheck.model_validate(obj, from_attributes=True)
    card = instance.model_dump(mode="json")
    schedule_id = obj.filtered_schedules[0].id if obj.filtered_schedules else None
    card["liked"] = bool(obj.filtered_cardlike)
    card["schedule_id"] = schedule_id
    return card
