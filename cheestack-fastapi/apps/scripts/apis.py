
# from datetime import datetime, timedelta, timezone
# from fastapi import APIRouter, Request, Security

# from apps.auth.deps import check_permissions
# from apps.study.consts import CardAssetType, CardType
# from apps.study.models import Book, Card
# from apps.tcyun.utils import Scos
# from core.exceptions import Sexception
# from sutils.tts import Stts
# from sutils.url import Surl
# from local_scripts.ttsdata import sentences
# from local_scripts.edge_tts_utils import EdgeTTS

# router = APIRouter(prefix="/v1/scripts")


# @router.get("/update_card", dependencies=[Security(check_permissions)])
# async def create_book(req: Request):
    
#     return {"message": "success"}


# @router.get("/create_card", dependencies=[Security(check_permissions)])
# async def create_card(req: Request):

#     for sentence in sentences:

#         for i in range(10):
#             try:    
#                 print(sentence)
#                 card = await Card.filter(question=sentence, books__id=55).first()
#                 card.created_at = datetime.now(timezone(timedelta(hours=8)))
#                 card.updated_at = datetime.now(timezone(timedelta(hours=8)))
#                 await card.save()
#                 break
#             except Exception as e:
#                 print(e)
#                 continue

#     # book = await Book.get_or_none(id=55, user=req.state.user).prefetch_related("cards")
#     # if not book:
#     #     raise Sexception(msg="数据未找到")
#     # user_id = req.state.user_id
#     # cards = []
#     # for sentence in sentences:
#     #     card = Card(user_id=user_id,
#     #             title=sentence,
#     #             type=CardType.READALOUD,
#     #             question=sentence,
#     #             )
#     #     cards.append(card)

#     # cards =  await Card.bulk_create(cards)
#     # await sleep(5)
    
#     # created_cards = await Card.filter(
#     #     created_at__gte=datetime(2025, 5, 1, 9, 22, 0, tzinfo=timezone(timedelta(hours=8)))
#     # )


#     # print(f"created_cards: {len(created_cards)}")

#     # await book.cards.add(*created_cards)

#     # return {"message": "success"}


#     # success = []

#     # cards = await Card.filter(books__id=55, extra__isnull=True)

#     # for card in cards:
#     #     try:
#     #         print(card.id)
#     #         await EdgeTTS.tts(card.title, "tmp.mp3")
#     #         filename = f"{card.title}.mp3"
#     #         key = Scos.get_key(filename, "card")
#     #         Scos.upload(key=key, path="tmp.mp3")
#     #         card.extra={"primaryAudioUrl": key}
#     #         await card.save()
#     #     except Exception as e:
#     #         print(e)
#     #         continue
#     return {"message": "success"}



# @router.get("/recorrect", response_model=dict)
# async def get_recorrect():
#     update_date = datetime(2025, 4, 23, 15, 38, 0, tzinfo=timezone(timedelta(hours=8)))
#     cards = await Card.filter(books__id=43, updated_at__lte=update_date).prefetch_related("card_assets__asset")
#     print(len(cards))


#     for card in cards[0:100]:
#         alter = False
#         question= card.question
#         extra = {
#             "question": card.question,
#             "answer": card.answer,
#         }
#         if (question == "noerrors"):
#             print("noerrors")
#         else:
#             print(question)
#             alter = True
#             extra['question'] = question
#             await Stts.tts_async(question, "tmp.mp3")
#         assets = card.card_assets
#         for asset in assets:
#             if asset.type == CardAssetType.PRIMARY_IMAGE:
#                 extra['primaryImageUrl'] = Surl.extract_path(asset.asset.url)
#             elif asset.type == CardAssetType.PRIMARY_AUDIO:
#                 extra['primaryAudioUrl'] = Surl.extract_path(asset.asset.url)
#                 if alter:
#                     Scos.upload(key=extra['primaryAudioUrl'], path="tmp.mp3")
#             elif asset.type == CardAssetType.SECONDARY_IMAGE:
#                 extra['secondaryImageUrl'] = Surl.extract_path(asset.asset.url)
#             elif asset.type == CardAssetType.SECONDARY_AUDIO:
#                 extra['secondaryAudioUrl'] = Surl.extract_path(asset.asset.url)
#         card.extra = extra
#         await card.save()

#     # await Stts.tts_async(cards[0].question, "test.mp3")
#     """
#     示例GET请求
#     :return: 示例数据
#     """
#     return {"message": "这是一个示例GET请求的响应"}

