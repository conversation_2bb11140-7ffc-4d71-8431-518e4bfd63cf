import asyncio
from typing import List
from fastapi import APIRouter, Security
import shortuuid
from sutils.log import Slog
from apps.auth.deps import check_permissions
from apps.pronunciation.models import Pronunciation
from apps.pronunciation.schema import PronuciationSchemaOut
from core.responses import ApiResponse
from apps.tcyun.utils import Scos
from sutils.tts import Stts
from sutils.models import Smodel
from sutils.string import Sstring
from tortoise.transactions import in_transaction

router = APIRouter(prefix="/v1/voice")


@router.post("/tts_english_word", dependencies=[Security(check_permissions)])
async def get_pronunciation(data: List[str]):
    # 1. 删除所有的标点符号并分词处理
    word_list = Sstring.split_sentence_to_word_list(data)
    Slog.debug(word_list)

    # 2. 批量查询数据库获取已存在的发音数据
    pronunciations = await Pronunciation.filter(name__in=word_list)
    found_words = {p.name: p for p in pronunciations}

    # 3. 并发生成缺失的语音文件
    async def generate_pronunciation(word):
        try:
            tmp_path = f"assets/{shortuuid.uuid()}.mp3"
            # 调用 TTS 生成语音文件
            await Stts.tts_async(word, tmp_path, rate="-40%")

            # 定义文件存储路径
            key = f"/assets/audios/official/{shortuuid.uuid()}/{word}.mp3"
            await asyncio.to_thread(Scos.upload, key, tmp_path)
            # 返回文件路径
            return Pronunciation(name=word, url=key)
        except Exception as e:
            Slog.debug(e)
            return None

    try:
        words = [word for word in word_list if word not in found_words]
        print(words)
        # 4. 并发生成和上传语音文件，并存储结果
        tasks = [generate_pronunciation(word) for word in word_list if word not in found_words]
        new_pronunciations = await asyncio.gather(*tasks, return_exceptions=True)
        Slog.debug(new_pronunciations)
        if new_pronunciations:
            # 5. 批量写入新的发音记录
            async with in_transaction(connection_name="master") as conn:
                await Pronunciation.bulk_create(
                    [pron for pron in new_pronunciations if isinstance(pron, Pronunciation)], using_db=conn
                )
    except Exception as e:
        Slog.debug(e)
    # 6. 最后从数据库获取所有相关的记录并返回
    rt: List[Pronunciation] = await Pronunciation.filter(name__in=word_list)
    return ApiResponse.success(data=Smodel.models_to_dict(rt, PronuciationSchemaOut))


# @router.post("", dependencies=[Security(check_permissions)])
# async def get_pronunciation(data: List[str]):
#     # 删除所有的标点符号
#     word_list = []
#     for word in data:
#         # word = Sstring.remove_punctuation(word).lower()
#         words = Sstring.split_sentence_by_punctuation(word)
#         word_list.extend(words)
#     # word_list去重
#     word_list = list(set(word_list))
#     # 批量查询数据库
#     pronunciations = await Pronunciation.filter(name__in=word_list)
#     found_words = {p.name: p for p in pronunciations}
#     # 构建结果列表
#     for word in word_list:
#         if word not in found_words:
#             try:
#                 tmp_path = f"assets/{shortuuid.uuid()}.mp3"
#                 # 判断`word`字符串是中文还是英文
#                 if Sstring.detect_language(word) == "en":
#                     await Stts.tts_async(word, tmp_path, voice=EdgeVoice.us_JennyNeural)
#                 elif Sstring.detect_language(word) == "cn":
#                     await Stts.tts_async(word, tmp_path, voice=EdgeVoice.zh_XiaoxiaoNeural)
#                 else:
#                     raise Sexception(data="语言不支持")
#                 key = f"/assets/audios/official/{shortuuid.uuid()}/{word}.mp3"
#                 Scos.upload(key, tmp_path)
#                 await Pronunciation.create(name=word, url=key)
#             except Exception as e:
#                 Slog.debug(e)
#     rt = await Pronunciation.filter(name__in=word_list)
#     return ApiResponse.success(data=Smodel.models_to_dict(rt, PronuciationSchemaOut))
