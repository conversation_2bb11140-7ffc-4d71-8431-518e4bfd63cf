from pydantic_settings import BaseSettings
from typing import Optional


# 阿里云短信帐号信息
ALISMS_ACCESS_KEY_ID = "LTAI5tF2K2Kc3zFNJuvCajzS"
ALISMS_ACCESS_KEY_SECRET = "******************************"
ALISMS_SIGN_NAME = "葱花科技"
ALISMS_TEMPLATE_CODE = "SMS_248840453"


class AuthConfig(BaseSettings):
    # 阿里云短信通知
    SMS_ACCESS_KEY_ID: str = "LTAI5tF2K2Kc3zFNJuvCajzS"
    SMS_ACCESS_KEY_SECRET: str = "******************************"
    SMS_SIGN_NAME: Optional[str] = "葱花科技"
    SMS_TEMPLATE_CODE: str = "SMS_248840453"


ali_settings = AuthConfig()
