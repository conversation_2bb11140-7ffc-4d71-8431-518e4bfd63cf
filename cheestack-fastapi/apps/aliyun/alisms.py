# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import json
import threading
from typing import Optional
from apps.aliyun.config import ali_settings
from sutils.log import Slog
from sutils.string import Sstring
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525.client import (
    Client as Dysmsapi20170525Client,
    dysmsapi_20170525_models,
    util_models,
)


class AliyunSms:
    client: Dysmsapi20170525Client
    sign_name: str
    template_code: str

    sign_name: str = (ali_settings.SMS_SIGN_NAME,)
    template_code: str = (ali_settings.SMS_TEMPLATE_CODE,)
    # 涉及到多线程使用时, 就要先对进程加锁, 让后面进程先等等, 因为前面的进程,可能在创建对象
    _instance_lock = threading.Lock()

    # 初始化对象的方法, 只执行一次
    def __init__(
        self,
        access_key_id: str,
        access_key_secret: str,
        sign_name: str,
        template_code: str,
    ):
        # 验证配置参数
        if not access_key_id or not access_key_secret:
            Slog.error("阿里云短信配置错误: AccessKey ID 或 AccessKey Secret 为空")
            raise ValueError("阿里云短信配置错误: AccessKey 信息不完整")

        if not sign_name or not template_code:
            Slog.error("阿里云短信配置错误: 签名或模板代码为空")
            raise ValueError("阿里云短信配置错误: 签名或模板代码不完整")

        self.sign_name = sign_name
        self.template_code = template_code

        Slog.info(f"初始化阿里云短信服务")
        Slog.debug(f"AccessKey ID: {access_key_id[:8]}***")
        Slog.debug(f"签名: {sign_name}")
        Slog.debug(f"模板代码: {template_code}")

        config = open_api_models.Config(
            # 必填，您的 AccessKey ID,
            access_key_id=access_key_id,
            # 必填，您的 AccessKey Secret,
            access_key_secret=access_key_secret,
        )
        # 访问的域名
        config.endpoint = "dysmsapi.aliyuncs.com"

        try:
            self.client = Dysmsapi20170525Client(config)
            Slog.info("阿里云短信客户端初始化成功")
        except Exception as e:
            Slog.error(f"阿里云短信客户端初始化失败: {e}")
            raise

    # 构建对象的方法, 只执行一次, 这个可以理解为类的构造函数, 构造完后才对调用 `__init__`进行参数的初始化
    def __new__(cls, *args, **kwargs):
        if not hasattr(AliyunSms, "_instance"):
            with AliyunSms._instance_lock:
                if not hasattr(AliyunSms, "_instance"):
                    AliyunSms._instance = object.__new__(cls)
        return AliyunSms._instance

    def send(
        self,
        mobile: str,
        captcha: str = Sstring.code_number(4),
    ) -> Optional[str]:
        Slog.info(f"开始发送短信验证码到手机号: {mobile}")
        Slog.debug(f"使用签名: {self.sign_name}, 模板代码: {self.template_code}")

        send_sms_request = dysmsapi_20170525_models.SendSmsRequest(
            phone_numbers=mobile,
            sign_name=self.sign_name,
            template_code=self.template_code,
            template_param=json.dumps(
                {"code": captcha},
            ),
        )

        Slog.debug(
            f"短信请求参数: phone_numbers={mobile}, sign_name={self.sign_name}, template_code={self.template_code}, template_param={json.dumps({'code': captcha})}"
        )

        try:
            rt = self.client.send_sms_with_options(send_sms_request, util_models.RuntimeOptions())

            # 详细记录响应信息
            Slog.info(f"阿里云短信API响应: code={rt.body.code}, message={rt.body.message}")
            Slog.debug(f"完整响应体: {rt.body}")

            if hasattr(rt.body, "request_id"):
                Slog.debug(f"请求ID: {rt.body.request_id}")
            if hasattr(rt.body, "biz_id"):
                Slog.debug(f"业务ID: {rt.body.biz_id}")

            # 返回验证码
            if rt.body.code == "OK":
                Slog.info(f"短信发送成功到手机号: {mobile}")
                return captcha
            else:
                Slog.error(f"短信发送失败: code={rt.body.code}, message={rt.body.message}")
                return None

        except Exception as error:
            Slog.error(f"短信发送异常: {type(error).__name__}: {str(error)}")
            Slog.error(f"异常详情: {error}")
            import traceback

            Slog.error(f"异常堆栈: {traceback.format_exc()}")
            return None


AliSms = AliyunSms(
    access_key_id=ali_settings.SMS_ACCESS_KEY_ID,
    access_key_secret=ali_settings.SMS_ACCESS_KEY_SECRET,
    sign_name=ali_settings.SMS_SIGN_NAME,
    template_code=ali_settings.SMS_TEMPLATE_CODE,
)
