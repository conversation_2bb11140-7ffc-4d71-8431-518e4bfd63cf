import logging
import os
import sys
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
from qcloud_cos.cos_exception import CosClientError, CosServiceError
from apps.tcyun.config import tc_settings
from sutils.file import Sfile
from sutils.datetime import Sdatetime
from sutils.log import Slog

class TencentCos:

    def __init__(self, region, bucket, secret_id, secret_key, scheme="https") -> None:
        # 正常情况日志级别使用 INFO，需要定位时可以修改为 DEBUG，此时 SDK 会打印和服务端的通信信息
        logging.basicConfig(level=logging.INFO, stream=sys.stdout)
        self.region = region
        self.bucket: str = bucket
        config = CosConfig(
            Region=region,
            SecretId=secret_id,
            SecretKey=secret_key,
            # Domain=domian,
            Scheme=scheme,  # 指定使用 http/https 协议来访问 COS，默认为 https，可不填
        )
        self.client = CosS3Client(config)

    def copy(self, key: str, src: str):
        """
        复制文件
        :param key: 目标文件名
        :param src: 源文件名
        """
        response: dict = None
        try:
            response = self.client.copy(
                Key=key,
                CopySource={"Bucket": self.bucket, "Key": src, "Region": self.region},
                Bucket=self.bucket,
            )
        except CosClientError or CosServiceError as e:
            print(e)
            return None
        return response

    def upload(self, key: str, path: str):
        """upload file to tencent cos
        @param key: cos object key
        @param local_file_path: local file path
        """
        response: dict = None
        # 使用高级接口断点续传，失败重试时不会上传已成功的分块(这里重试10次)
        for i in range(0, 3):
            try:
                response = self.client.upload_file(
                    Key=key,
                    LocalFilePath=path,
                    Bucket=self.bucket,
                )
                os.remove(path)
                break
            except Exception as e:
                Slog.debug()(e)
                os.remove(path)
                return None
        return response

    def delete(self, key: str):
        if not key:
            return False
        response: dict = None
        try:
            response = self.client.delete_object(Bucket=self.bucket, Key=key)
        except CosClientError or CosServiceError as e:
            print(e)
            return None
        return response

    def get_key(self, filename, location):
        """
        @param filename: 文件名
        @param location: 存储位置
        @return: cos object key
        """
        type = Sfile.get_file_type(filename)
        key = f"/cheestack/assets/{type}/{location}/{Sdatetime.unique_time_string()}/{filename}"
        return key


Scos = TencentCos(
    region=tc_settings.COS_REGION,
    bucket=tc_settings.COS_BUCKET,
    secret_id=tc_settings.COS_SECRET_ID,
    secret_key=tc_settings.COS_SECRET_KEY,
)
