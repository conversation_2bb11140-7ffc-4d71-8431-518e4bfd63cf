from pydantic_settings import BaseSettings


class AuthConfig(BaseSettings):
    # 腾讯云对象存储信息
    COS_SECRET_ID: str = "AKID346GNOD2lLjsmSu6AFDya8lFnGWigQxN"
    COS_SECRET_KEY: str = "Sr6GF3pTv9INKGyRgjgPuAEygDfwzoYJ"
    COS_BASE_URL: str = "https://cheestack-1314772501.cos.ap-guangzhou.myqcloud.com"
    COS_REGION: str = "ap-guangzhou"
    COS_BUCKET: str = "cheestack-1314772501"

tc_settings = AuthConfig()
