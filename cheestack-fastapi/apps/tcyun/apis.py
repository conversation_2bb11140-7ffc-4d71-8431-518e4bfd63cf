from fastapi import APIRouter, Request, Security
from sts.sts import Sts
from apps.tcyun.config import tc_settings
from apps.auth.deps import (
    check_permissions,
)
from core.exceptions import Sexception
from core.responses import ApiResponse
from sutils.log import Slog


# 分支路由/tcyun
router = APIRouter(prefix="/v1/tcyun")


@router.get("/cos_token", tags=["CosToken接口"], dependencies=[Security(check_permissions)])
async def get_federation_token(req: Request):
    """
    获取临时访问token
    doc: https://github.com/tencentyun/qcloud-cos-sts-sdk/blob/master/python/demo/sts_demo.py
    :return:
    """
    file_prefix = f"/userdata/{req.state.user_id}"
    Slog.debug(file_prefix)

    # 配置文件
    config = {
        # 请求URL，域名部分必须和domain保持一致
        "url": "https://sts.tencentcloudapi.com/",
        # 域名，非必须，默认为 sts.tencentcloudapi.com
        "domain": "sts.tencentcloudapi.com",
        # 临时密钥有效时长，单位是秒
        "duration_seconds": 1800,  # 30分钟
        # 固定密钥
        "secret_id": tc_settings.COS_SECRET_ID,
        "secret_key": tc_settings.COS_SECRET_KEY,
        # 换成你的 bucket
        "bucket": tc_settings.COS_BUCKET,
        # 换成 bucket 所在地区
        "region": tc_settings.COS_REGION,
        # 这里改成允许的路径前缀，可以根据自己网站的用户登录态判断允许上传的具体路径
        "allow_prefix": f"{file_prefix}/*",  # 允许的前缀
        # 密钥的权限列表
        "allow_actions": [
            # 删除对象权限
            "name/cos:DeleteObject",
            # 查询存储桶跨域配置
            "name/cos:GetBucketCORS",
            # 简单上传
            "name/cos:PutObject",
            # 获取对象
            "name/cos:GetObject",
            # 分片上传
            "name/cos:InitiateMultipartUpload",
            "name/cos:ListMultipartUploads",
            "name/cos:ListParts",
            "name/cos:UploadPart",
            "name/cos:CompleteMultipartUpload",
        ],
    }

    try:
        sts = Sts(config)
        response = sts.get_credential()
        data = dict(response)
        credentials: dict = data.get("credentials", None)
        if not credentials:
            return ApiResponse.fail(msg="接口故障，请稍后再试!")
        res = {
            "tmp_secret_id": credentials.get("tmpSecretId", None),
            "tmp_secret_key": credentials.get("tmpSecretKey", None),
            "session_token": credentials.get("sessionToken", None),
            "start_time": data.get("startTime", None),
            "expired_time": data.get("expiredTime", None),
            "bucket": config.get("bucket", None),
            "region": config.get("region", None),
            "file_path": f"{file_prefix}",
        }
        return ApiResponse.success(data=res)
    except Exception as e:
        Slog.debug(e)
        raise Sexception(msg="接口故障，请稍后再试!")
