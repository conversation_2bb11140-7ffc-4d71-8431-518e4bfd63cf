# -*- coding:utf-8 -*-
"""
@Time : 2022/4/27 3:40 PM
@Author: binkuolo
@Des: JWT鉴权
"""
from datetime import UTC, datetime, timedelta
import time
from xml.dom import ValidationErr
from sutils.log import Slog
from sutils.models import Smodel
from apps.auth.config import auth_settings
import jwt
from fastapi import Depends, HTTPException, Request, Response
from fastapi.security import SecurityScopes
from fastapi.security.oauth2 import OAuth2PasswordBearer
from jwt import PyJWTError
from passlib.handlers.pbkdf2 import pbkdf2_sha256
from starlette import status
from tortoise.transactions import in_transaction
from apps.auth.models import Access, AccessLog, User, Device
from apps.auth.schema import (
    AuthModel,
    ConfigSchemaOut,
    UserPersonalSchemaOut,
)
from core import exceptions
from core.redis import Scache
from core.responses import ApiResponse

# 这里的url参数可以直接传空字符串
OAuth2 = OAuth2PasswordBearer(
    tokenUrl=auth_settings.TOKEN_URL,
    # scheme_name="User",
    # scopes={"is_superuser": "超级管理员", "is_admin": "普通管理员"},
)

# 加密生成token字符串
def create_access_token(data: dict):
    """
    创建token
    :param data: 加密数据
    :return: jwt
    """
    token_data = data.copy()
    # token超时时间
    expire = datetime.now(UTC) + timedelta(minutes=auth_settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    # 向jwt加入超时时间
    token_data.update({"exp": expire})
    # jwt加密
    jwt_token = jwt.encode(token_data, auth_settings.JWT_SECRET_KEY, auth_settings.JWT_ALGORITHM)
    return jwt_token


# 创建token
async def check_permissions(
    req: Request, security_scopes: SecurityScopes, token=Depends(OAuth2)
):
    """
    权限验证
    :param token:
    :param req:
    :param security_scopes: 权限域
    :return:
    """
    # ----------------------------------------验证JWT token------------------------------------------------------------
    try:
        # token解密
        payload = jwt.decode(token, auth_settings.JWT_SECRET_KEY, algorithms=[auth_settings.JWT_ALGORITHM])
        if payload:
            # 用户ID
            id = payload.get("id", None)
            # 设备ID
            device_id = payload.get("device_id", None)
            # 用户类型
            is_superuser = payload.get("is_superuser", None)
            # 无效用户信息
            if id is None or is_superuser is None:
                credentials_exception = HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效凭证",
                    headers={"WWW-Authenticate": f"Bearer{token}"},
                )
                raise credentials_exception
        else:
            credentials_exception = HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效凭证",
                headers={"WWW-Authenticate": f"Bearer {token}"},
            )
            raise credentials_exception

    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="凭证已证过期",
            headers={"WWW-Authenticate": f"Bearer {token}"},
        )

    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效凭证",
            headers={"WWW-Authenticate": f"Bearer {token}"},
        )

    except (PyJWTError, ValidationErr):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效凭证",
            headers={"WWW-Authenticate": f"Bearer {token}"},
        )
    # ---------------------------------------验证权限-------------------------------------------------------------------
    # 查询用户是否真实有效、或者已经被禁用
    check_user = await User().get_or_none(id=id)
    if not check_user or check_user.status != 1:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已经被管理员禁用!",
            headers={"WWW-Authenticate": f"Bearer {token}"},
        )

    # 判断是否设置了权限域
    if security_scopes.scopes:
        # 非超级管理员且当前域需要验证
        if not is_superuser and security_scopes.scopes:
            # 未查询用户是否有对应权限
            is_pass = await Access.filter(
                role__user__id=id,
                is_check=True,
                scopes__in=set(security_scopes.scopes),
            ).all()

            if not is_pass:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not permissions",
                    headers={"scopes": security_scopes.scope_str},
                )
    # 缓存用户模型
    req.state.user = check_user
    # 缓存用户ID
    req.state.user_id = id
    # 缓存设备ID
    req.state.device_id = device_id
    # 缓存用户类型
    req.state.is_superuser = is_superuser

    # 验证设备是否有效（如果有设备ID）
    if device_id:
        device = await Device.get_or_none(id=device_id, user_id=id, is_active=True)
        if not device:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="设备已失效，请重新登录",
                headers={"WWW-Authenticate": f"Bearer {token}"},
            )
        # 更新设备最后活跃时间
        device.last_active = datetime.now(UTC)
        await device.save()


async def limit_frequent_captcha_requests(data: AuthModel):
    CAPTCHA_CACHE_TIMEOUT = 60  # 定义请求间隔时间（秒）
    cached_data = Scache.get_json(data.mobile)
    if cached_data:
        last_request_time = cached_data.get("timestamp", 0)
        if time.time() - last_request_time < CAPTCHA_CACHE_TIMEOUT:
            raise HTTPException(status_code=400, detail="请求过于频繁，请稍候再试")


def store_captcha(mobile: str, captcha: str):
    Scache.set_json(mobile, {"captcha": captcha, "timestamp": int(time.time())}, 300)  # 5分钟

# 验证短信验证码是否正确
def check_captcha(mobile: str, captcha: str) -> bool:
    """
    短信验证码校验
    :param captcha: 短信验证码
    :param mobile: 手机号码
    :return: bool
    """
    # 获取redis中的验证码
    cache_captcha = Scache.get_json(mobile).get("captcha")
    # Slog.debug(f"{cache_captcha}-{data.captcha}")
    # 比对验证码
    if cache_captcha and captcha == cache_captcha:
        Scache.delete(mobile)
        return True
    else:
        raise exceptions.Sexception(msg="验证码不正确")


# get user public info
async def get_user_pulic_info(user_id: str):
    user = await User.get_or_none(id=user_id)
    if user:
        return user
    raise exceptions.mysql_does_not_exist


async def write_access_log(req: Request, user_id: str, note: str = None):
    """
    写入访问日志
    :param user_id: 用户ID
    :param req: 请求头
    :param note: 备注
    :return:
    """
    data = {
        "user_id": user_id,
        "target_url": req.get("path"),
        "user_agent": req.headers.get("user-agent"),
        "request_params": {
            "method": req.method,
            "params": dict(req.query_params),
            "body": bytes(await req.body()).decode(),
        },
        "ip": req.headers.get("x-forwarded-for"),
        "note": note,
    }
    await AccessLog.create(**data)


async def get_auth_user(req: Request) -> User:
    get_user = await User.get_or_none(id=req.state.user_id)
    if not get_user:
        raise exceptions.mysql_does_not_exist()
    return get_user


def en_password(psw: str):
    """
    密码加密
    :param psw: 需要加密的密码
    :return: 加密后的密码
    """
    password = pbkdf2_sha256.hash(psw)
    return password


def check_password(new: str, old: str):
    """
    密码校验
    :param password: 用户输入的密码
    :param old: 数据库密码
    :return: Boolean
    """
    check = pbkdf2_sha256.verify(new, old)
    if check:
        return True
    else:
        return False


def get_auth_response(user: User, response: Response) -> str:
    jwt_data = {
        "id": user.pk,
        "copyright": "chivetech",
        "ai_voice_secret": "chivetechaivoice-dslddKHD9d70kj3Hd5dgs$dh",
        "is_superuser": user.is_superuser,
    }
    jwt_token = create_access_token(data=jwt_data)
    response.headers["Set-Cookie"] = "X-token=Bearer " + jwt_token

    return ApiResponse.success(
        data={
            "auth": {"id": user.id, "access_token": jwt_token, "refresh_token": jwt_token},
            "user": Smodel.model_to_dict(user, UserPersonalSchemaOut),
            "config": Smodel.model_to_dict(user.config, ConfigSchemaOut),
        },
    )


async def get_or_create_device(user: User, request: Request, device_info: dict = None) -> Device:
    """
    获取或创建设备记录
    """
    user_agent = request.headers.get("user-agent", "")
    ip_address = request.headers.get("x-forwarded-for") or request.client.host

    # 尝试根据设备信息查找现有设备
    device = None
    if device_info and device_info.get("device_id"):
        device = await Device.get_or_none(id=device_info["device_id"], user=user)

    if not device:
        # 根据设备指纹查找
        device = await Device.filter(user=user, user_agent=user_agent, is_active=True).first()

    if not device:
        # 创建新设备
        device_data = {
            "user": user,
            "user_agent": user_agent,
            "ip_address": ip_address,
            "device_name": device_info.get("device_name") if device_info else None,
            "device_type": device_info.get("device_type") if device_info else None,
            "device_model": device_info.get("device_model") if device_info else None,
            "os_version": device_info.get("os_version") if device_info else None,
            "app_version": device_info.get("app_version") if device_info else None,
            "device_token": device_info.get("device_token") if device_info else None,
        }
        device = await Device.create(**device_data)
    else:
        # 更新现有设备信息
        update_data = {
            "last_active": datetime.now(UTC),
            "login_count": device.login_count + 1,
            "ip_address": ip_address,
        }
        if device_info:
            if device_info.get("device_name"):
                update_data["device_name"] = device_info["device_name"]
            if device_info.get("device_token"):
                update_data["device_token"] = device_info["device_token"]
            if device_info.get("app_version"):
                update_data["app_version"] = device_info["app_version"]

        await device.update_from_dict(update_data)
        await device.save()

    return device


def create_access_token_with_device(user: User, device: Device) -> str:
    """
    创建包含设备信息的访问令牌
    """
    jwt_data = {
        "id": user.pk,
        "device_id": device.id,
        "copyright": "chivetech",
        "ai_voice_secret": "chivetechaivoice-dslddKHD9d70kj3Hd5dgs$dh",
        "is_superuser": user.is_superuser,
    }
    return create_access_token(data=jwt_data)


def generate_random_password(length: int = 12) -> str:
    """
    生成随机密码（用于自动注册）
    """
    import secrets
    import string

    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return "".join(secrets.choice(alphabet) for _ in range(length))


async def generate_default_username(mobile: str) -> str:
    """
    生成默认用户名，确保唯一性
    策略：使用随机字符串，如果重复则重新生成，直到唯一为止
    """
    import shortuuid
    from apps.auth.models import User

    max_attempts = 100  # 最大尝试次数，防止无限循环
    for _ in range(max_attempts):
        # 生成格式：用户_随机8位字符
        username = f"用户_{shortuuid.uuid()[:8]}"
        if not await User.exists(username=username):
            return username

    # 如果100次都重复（几乎不可能），使用完整的shortuuid
    return f"用户_{shortuuid.uuid()}"


async def quick_login_or_register(
    mobile: str, captcha: str, request: Request, skip_captcha: bool = False
) -> tuple[User, bool, Device]:
    """
    一键登录或自动注册
    返回: (用户对象, 是否为新用户, 设备对象)
    """
    # 1. 验证验证码（可跳过）
    if not skip_captcha:
        check_captcha(mobile, captcha)
    else:
        Slog.info(f"跳过验证码验证: {mobile} (captcha: {captcha})")

    # 2. 检查用户是否存在
    user = await User.get_or_none(mobile=mobile, status=1)
    is_new_user = False

    if not user:
        # 3. 用户不存在，自动注册
        from apps.auth.models import Config

        async with in_transaction(connection_name="master"):
            try:
                user = await User.create(
                    mobile=mobile,
                    username=await generate_default_username(mobile),
                    password=en_password(generate_random_password()),
                    status=1,
                )
                # 创建用户配置
                await Config.create(user=user)
                is_new_user = True
                Slog.info(f"自动注册新用户: {mobile}")
            except Exception as e:
                Slog.error(f"自动注册失败: {e}")
                raise exceptions.Sexception(msg="注册失败，请稍后重试")

    # 4. 重新获取用户信息（包含关联数据）
    user = await User.get_or_none(id=user.id).prefetch_related(
        "config", "config__editing_book", "config__current_study"
    )

    # 5. 处理设备信息
    device = await get_or_create_device(user, request)

    return user, is_new_user, device
