# 用户认证系统 (Auth)

> CheeStack 用户认证和权限管理模块

## 📖 功能概述

### 主要职责
用户认证系统负责处理用户注册、登录、权限验证等核心功能，为整个应用提供安全的用户身份管理服务。

- **用户管理**: 用户注册、登录、信息更新
- **权限控制**: 基于角色的访问控制 (RBAC)
- **Token管理**: JWT token生成、验证、刷新
- **验证码服务**: 手机短信验证码发送和验证

### 技术实现
- **认证方式**: JWT Bearer Token
- **密码加密**: Passlib + bcrypt
- **验证码**: 阿里云短信服务
- **权限模型**: 用户-角色-权限三层模型

## 🏗️ 架构设计

### 核心组件
```
apps/auth/
├── __init__.py
├── apis.py                     # API路由定义
├── models.py                   # 数据模型 (User, Role, Config)
├── schema.py                   # Pydantic验证模型
├── deps.py                     # 依赖注入函数
└── utils.py                    # 工具函数
```

### 数据模型
```python
# 用户模型
class User:
    id: str                     # 用户ID (shortuuid)
    username: str               # 用户名
    mobile: str                 # 手机号
    email: str                  # 邮箱
    password: str               # 加密密码
    avatar: str                 # 头像URL
    intro: str                  # 个人简介
    is_superuser: bool          # 是否超级管理员
    status: int                 # 用户状态 (1正常,2禁用,3已删除)
    role: List[Role]            # 用户角色 (多对多)

# 角色模型
class Role:
    name: str                   # 角色名称
    description: str            # 角色描述
    permissions: List[str]      # 权限列表

# 配置模型
class Config:
    key: str                    # 配置键
    value: str                  # 配置值
    description: str            # 配置说明

# 设备模型
class Device:
    id: str                     # 设备ID (shortuuid)
    user_id: str                # 用户ID
    device_name: str            # 设备名称
    device_type: str            # 设备类型 (iOS/Android/Web)
    device_model: str           # 设备型号
    os_version: str             # 操作系统版本
    app_version: str            # 应用版本
    device_token: str           # 设备推送Token
    user_agent: str             # 用户代理
    ip_address: str             # IP地址
    last_active: datetime       # 最后活跃时间
    is_active: bool             # 是否活跃
    login_count: int            # 登录次数
```

### 数据流
```
用户请求 → JWT验证 → 权限检查 → 业务逻辑 → 响应返回
```

## 🔌 API接口

### 认证相关
- `POST /v1/auth/get_captcha` - 获取手机验证码
- `POST /v1/auth/register` - 用户注册
- `POST /v1/auth/login` - 用户登录
- `POST /v1/auth/refresh` - 刷新Token
- `POST /v1/auth/logout` - 用户登出

### 用户管理
- `GET /v1/auth/profile` - 获取用户信息
- `PUT /v1/auth/profile` - 更新用户信息
- `POST /v1/auth/upload_avatar` - 上传用户头像
- `PUT /v1/auth/change_password` - 修改密码

### 请求示例
```python
# 获取验证码
POST /v1/auth/get_captcha
{
    "mobile": "13800138000",
    "type": "register"  # register, login, reset
}

# 用户注册
POST /v1/auth/register
{
    "mobile": "13800138000",
    "password": "password123",
    "captcha": "123456",
    "username": "testuser"
}

# 用户登录
POST /v1/auth/login
{
    "mobile": "13800138000",
    "password": "password123",
    "device_info": {  # 可选的设备信息
        "device_name": "iPhone 15",
        "device_type": "iOS",
        "device_model": "iPhone15,2",
        "os_version": "17.0",
        "app_version": "1.0.0",
        "device_token": "push-token-here"
    }
}

# 响应格式
{
    "success": true,
    "data": {
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "bearer",
        "expires_in": 3600,
        "user": {
            "id": "user-id",
            "username": "testuser",
            "mobile": "13800138000",
            "avatar": "https://cdn.example.com/avatar.jpg"
        },
        "device": {
            "id": "device-id",
            "device_name": "iPhone 15",
            "device_type": "iOS",
            "last_active": "2025-01-19T15:30:00Z",
            "is_active": true,
            "login_count": 5
        }
    }
}

# 设备管理API
# 获取我的设备列表
GET /v1/auth/devices/me
Authorization: Bearer <token>

# 更新设备信息
PUT /v1/auth/devices/{device_id}
Authorization: Bearer <token>
{
    "device_name": "新设备名称",
    "device_token": "new-push-token"
}

# 停用设备
DELETE /v1/auth/devices/{device_id}
Authorization: Bearer <token>

# 注册新设备
POST /v1/auth/devices/register
Authorization: Bearer <token>
{
    "device_name": "iPad Pro",
    "device_type": "iOS",
    "device_model": "iPad14,3",
    "os_version": "17.0",
    "app_version": "1.0.0",
    "device_token": "push-token-here"
}
```

## 🔒 权限控制

### 权限检查装饰器
```python
from apps.auth.deps import check_permissions

# 需要登录
@router.get("/protected")
async def protected_route(user: User = Security(check_permissions)):
    return {"user_id": user.id}

# 需要特定权限
@router.get("/admin")
async def admin_route(user: User = Security(check_permissions, scopes=["admin"])):
    return {"message": "Admin access granted"}
```

### 权限级别
- **公开访问**: 无需认证的接口
- **登录用户**: 需要有效JWT token
- **管理员**: 需要管理员角色
- **超级管理员**: 需要超级管理员权限

## 🔧 核心功能

### JWT Token管理
```python
# Token生成
def create_access_token(user_id: str, expires_delta: timedelta = None):
    to_encode = {"sub": user_id, "type": "access"}
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=24)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

# Token验证
def verify_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        return user_id
    except JWTError:
        raise credentials_exception
```

### 密码加密
```python
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 密码加密
def hash_password(password: str) -> str:
    return pwd_context.hash(password)

# 密码验证
def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)
```

### 验证码服务
```python
# 发送验证码
async def send_captcha(mobile: str, captcha_type: str):
    code = generate_random_code(6)
    # 存储到Redis，设置5分钟过期
    await redis.setex(f"captcha:{mobile}:{captcha_type}", 300, code)
    # 发送短信
    await send_sms(mobile, code, captcha_type)

# 验证验证码
async def verify_captcha(mobile: str, code: str, captcha_type: str):
    stored_code = await redis.get(f"captcha:{mobile}:{captcha_type}")
    if not stored_code or stored_code != code:
        raise Sexception(msg="验证码错误或已过期")
    # 验证成功后删除验证码
    await redis.delete(f"captcha:{mobile}:{captcha_type}")
```

## 🧪 测试

### 单元测试
```python
@pytest.mark.asyncio
async def test_user_registration():
    # 测试用户注册功能
    user_data = {
        "mobile": "13800138000",
        "password": "password123",
        "captcha": "123456",
        "username": "testuser"
    }
    response = await client.post("/v1/auth/register", json=user_data)
    assert response.status_code == 200
    assert response.json()["success"] is True

@pytest.mark.asyncio
async def test_user_login():
    # 测试用户登录功能
    login_data = {
        "mobile": "13800138000",
        "password": "password123"
    }
    response = await client.post("/v1/auth/login", json=login_data)
    assert response.status_code == 200
    assert "access_token" in response.json()["data"]
```

### 集成测试
- 完整的注册-登录-权限验证流程测试
- Token过期和刷新机制测试
- 权限控制和访问限制测试

## 🔧 配置说明

### 环境变量
```env
# JWT配置
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRE_HOURS=24

# 短信服务配置
SMS_ACCESS_KEY=your-access-key
SMS_SECRET_KEY=your-secret-key
SMS_SIGN_NAME=your-sign-name
SMS_TEMPLATE_CODE=your-template-code

# Redis配置 (用于验证码存储)
REDIS_URL=redis://localhost:6379/0
```

### 数据库配置
```python
# 用户表索引
CREATE INDEX idx_user_mobile ON user(mobile);
CREATE INDEX idx_user_username ON user(username);
CREATE INDEX idx_user_status ON user(status);
```

## 📝 开发注意事项

### 安全考虑
- 密码必须进行bcrypt加密存储
- JWT token设置合理的过期时间
- 验证码限制发送频率和有效期
- 敏感操作需要二次验证

### 性能优化
- 使用Redis缓存用户会话信息
- 合理设置数据库索引
- 避免频繁的权限查询

### 常见问题
- **Token过期**: 实现自动刷新机制
- **验证码失效**: 检查Redis连接和过期时间
- **权限不足**: 确认用户角色和权限配置
- **密码错误**: 检查密码加密和验证逻辑

## 🔗 依赖关系
- **内部依赖**: core.models, core.exceptions, sutils.string
- **外部依赖**: fastapi, tortoise-orm, pyjwt, passlib
- **服务依赖**: Redis (验证码存储), 阿里云短信服务

---

**注意**: 认证系统是整个应用的安全基础，任何修改都需要经过充分测试，确保不会影响系统安全性。
