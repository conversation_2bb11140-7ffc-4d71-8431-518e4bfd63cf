from datetime import datetime
from pydantic import BaseModel, Field
from tortoise.contrib.pydantic import pydantic_model_creator

from apps.auth.models import User, Config, Device
from sutils.validator import Spattern

# 用于写入, exclude_readonly 排除所有只读的字段, id为自动生成
UserSchema = pydantic_model_creator(User, name="UserSchema", exclude=("password",))
ConfigSchema = pydantic_model_creator(Config, name="ConfigSchema")


class UserSchemaIn(BaseModel):
    avatar: str | None = None
    username: str | None = None
    intro: str | None = None

    class Config:
        from_attributes = True
        extra = "ignore"


class CaptchaCheckSchema(BaseModel):
    mobile: str = Field(pattern=Spattern.mobile, description="手机号")
    captcha: str


class UpdatePasswordSchema(CaptchaCheckSchema):
    password: str = Field(min_length=6, max_length=20, description="密码")


class UpdateMobileSchema(CaptchaCheckSchema):
    username: str
    password: str = Field(min_length=6, max_length=20, description="密码")


# 一键登录Schema
class QuickLoginSchema(BaseModel):
    mobile: str = Field(pattern=Spattern.chiness_mobile, description="手机号")
    captcha: str = Field(min_length=4, max_length=6, description="验证码")

    class Config:
        from_attributes = True
        extra = "ignore"


# Device Schema 定义
class DeviceSchemaIn(BaseModel):
    device_name: str | None = Field(None, max_length=100, description="设备名称")
    device_type: str | None = Field(None, max_length=50, description="设备类型")
    device_model: str | None = Field(None, max_length=100, description="设备型号")
    os_version: str | None = Field(None, max_length=50, description="操作系统版本")
    app_version: str | None = Field(None, max_length=50, description="应用版本")
    device_token: str | None = Field(None, max_length=255, description="设备推送Token")

    class Config:
        from_attributes = True
        extra = "ignore"


# 登录用的model
class AuthModel(BaseModel):
    mobile: str | None = Field(None, pattern=Spattern.chiness_mobile)
    # username字段支持用户名或手机号登录，使用宽松的验证规则
    username: str | None = Field(None, pattern=r"^.{1,30}$", description="用户名或手机号")
    password: str | None = Field(None, pattern=Spattern.password)
    captcha: str | None = None
    easy_mode: bool | None = True
    new_password: str | None = None
    # 设备信息（可选）
    device_info: DeviceSchemaIn | None = None


class UserPublicSchemaOut(BaseModel):
    # 包含Role外键信息的模型
    username: str
    intro: str | None = None
    avatar: str | None = None

    class Config:
        from_attributes = True


# 包含所有信息模型
UserAdminSchema = pydantic_model_creator(
    User,
    name="UserAdminSchema",
)


class ConfigEditingBookSchemaOut(BaseModel):
    id: int
    name: str

    class Config:
        from_attributes = True


class ConfigSchemaOut(ConfigSchema):
    current_study_id: int | None = None
    editing_book: ConfigEditingBookSchemaOut | None = None
    editing_book_id: int | None = None

    class Config:
        from_attributes = True
        # 允许额外字段


class ConfigSchemaIn(ConfigSchema):
    id: int = None
    current_study_id: int | None = None
    editing_book_id: int | None = None

    class Config:
        extra = "ignore"  # 允许额外字段


class UserPersonalSchemaOut(UserSchema):
    # config: ConfigSchemaOut | None = None

    class Config:
        from_attributes = True


# Device 相关 Schema
DeviceSchema = pydantic_model_creator(Device, name="DeviceSchema")


class DeviceSchemaOut(BaseModel):
    id: str
    device_name: str | None = None
    device_type: str | None = None
    device_model: str | None = None
    os_version: str | None = None
    app_version: str | None = None
    last_active: datetime
    is_active: bool
    login_count: int
    created_at: datetime

    class Config:
        from_attributes = True


class AuthSchemaOut(BaseModel):
    id: str
    access_token: str
    refresh_token: str


class AuthSchemaResponse(BaseModel):
    auth: AuthSchemaOut
    user: UserPersonalSchemaOut
    config: ConfigSchemaOut
    device: DeviceSchemaOut | None = None
    is_new_user: bool = False  # 标识是否为新注册用户


class DeviceListSchemaOut(BaseModel):
    devices: list[DeviceSchemaOut]
    current_device_id: str | None = None

    class Config:
        from_attributes = True
