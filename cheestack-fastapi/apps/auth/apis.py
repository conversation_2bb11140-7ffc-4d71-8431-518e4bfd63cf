from fastapi import File, Response, UploadFile
from fastapi import APIRouter, Depends, HTTPException, Request, Security
from tortoise.expressions import Q
from apps.aliyun.alisms import AliSms
from apps.auth.deps import (
    check_captcha,
    check_password,
    check_permissions,
    limit_frequent_captcha_requests,
    en_password,
    get_auth_user,
    store_captcha,
    get_or_create_device,
    quick_login_or_register,
    create_access_token_with_device,
    generate_default_username,
)
from apps.auth.models import User, Config, Device
from apps.auth.schema import (
    AuthModel,
    AuthSchemaResponse,
    ConfigSchemaIn,
    ConfigSchemaOut,
    UpdateMobileSchema,
    UpdatePasswordSchema,
    UserPersonalSchemaOut,
    UserPublicSchemaOut,
    UserSchemaIn,
    DeviceSchemaIn,
    DeviceSchemaOut,
    DeviceListSchemaOut,
    QuickLoginSchema,
)
from apps.study.dependencies import uploadFileFromUploadFile
from apps.study.models import Book
from apps.tcyun.utils import Scos
from core.exceptions import Sexception
from core.responses import ApiResponse
from sutils.log import Slog
from sutils.string import Sstring
from sutils.models import Smodel
from tortoise.transactions import in_transaction
from sutils.url import Surl

from core.schema import BaseResponseModel, resmod

# 分支路由/auth
router = APIRouter(prefix="/v1/auth")


# 获取验证码
@router.post(
    "/get_captcha",
    summary="获取手机验证码",
    description="用于注册/登陆/重置等,目前仅支持国内手机号",
    response_model=BaseResponseModel,
)
async def get_captcha(data: AuthModel, _: None = Depends(limit_frequent_captcha_requests)):
    mobile = data.mobile
    Slog.info(f"收到获取验证码请求，手机号: {mobile}")

    # 生成4位验证码
    generated_captcha = Sstring.code_number(4)
    Slog.debug(f"生成的验证码: {generated_captcha}")

    # 通过阿里云发送手机验证码
    Slog.info(f"开始调用阿里云短信服务发送验证码")
    captcha = AliSms.send(mobile, generated_captcha)

    if not captcha:
        Slog.error(f"阿里云短信发送失败，手机号: {mobile}")
        raise HTTPException(status_code=500, detail="验证码发送失败，请稍后重试")

    Slog.info(f"验证码发送成功，手机号: {mobile}，验证码: {captcha}")

    # 存储验证码到缓存
    try:
        store_captcha(mobile, captcha)
        Slog.debug(f"验证码已存储到缓存，手机号: {mobile}")
    except Exception as e:
        Slog.error(f"验证码存储失败: {e}")
        raise HTTPException(status_code=500, detail="验证码存储失败")

    return ApiResponse.success()


# 用户注册
@router.post(
    "/register",
    summary="用户注册",
    response_model=resmod(AuthSchemaResponse),
)
async def register(data: AuthModel, response: Response, request: Request):

    check_captcha(data.mobile, data.captcha)  # 检查验证码
    # 转为小写
    data.username = data.username.lower()
    # 确保用户没有注册
    user: User | None = await User.filter(Q(mobile=data.mobile) | Q(username=data.username)).first()
    if user:
        raise Sexception(msg="用户名无效")
    async with in_transaction(connection_name="master"):
        try:
            user = await User.create(
                username=data.username if data.username else await generate_default_username(data.mobile),
                mobile=data.mobile,
                password=en_password(data.password),
            )
            await Config.create(user=user)
        except Exception as e:
            Slog.debug(e)
            raise Sexception(msg="操作失败")
        # 创建时user还有没config.id,从新读取数据返回
    user = await User.get_or_none(id=user.id).prefetch_related(
        "config", "config__editing_book", "config__current_study"
    )

    # 处理设备信息
    device_info = data.device_info.model_dump() if data.device_info else None
    device = await get_or_create_device(user, request, device_info)

    # 创建包含设备信息的JWT Token
    from apps.auth.deps import create_access_token_with_device

    jwt_token = create_access_token_with_device(user, device)
    response.headers["Set-Cookie"] = "X-token=Bearer " + jwt_token

    return ApiResponse.success(
        data={
            "auth": {"id": user.id, "access_token": jwt_token, "refresh_token": jwt_token},
            "user": Smodel.model_to_dict(user, UserPersonalSchemaOut),
            "config": Smodel.model_to_dict(user.config, ConfigSchemaOut),
            "device": Smodel.model_to_dict(device, DeviceSchemaOut),
        },
    )


# 删除用户
@router.delete(
    "/me",
    summary="删除当前用户账号",
    dependencies=[Security(check_permissions)],
    response_model=BaseResponseModel,
)
async def delete_user(
    data: dict,
    user: User = Depends(get_auth_user),
):
    """
    删除当前用户账号，需要验证密码
    """
    password = data.get("password")
    if not password:
        raise Sexception(msg="密码不能为空")

    # 验证密码是否正确
    if not check_password(password, user.password):
        raise Sexception(msg="密码错误")
    user.status = 3
    await user.save()
    return ApiResponse.success(msg="账号已删除")


# 登录或注册
@router.post(
    "/login",
    summary="用户登陆",
    response_model=resmod(AuthSchemaResponse),
)
async def login(data: AuthModel, response: Response, request: Request):
    """
    账号或手机号登陆
    :param data: 登录数据
    :param response: 响应对象
    :param request: 请求对象
    :return: jwt token
    """
    # 转为小写
    data.username = data.username.lower()
    print(data.username)
    user = (
        await User.filter(Q(mobile=data.username) | Q(username=data.username))
        .prefetch_related("config", "config__editing_book", "config__current_study")
        .first()
    )
    if not user or user.status == 3:
        raise Sexception(msg="用户不存在")
    Slog.debug(data.username)
    # 验证密码是否正确
    if not check_password(data.password, user.password):
        raise Sexception(msg="帐号或密码错误")

    # 处理设备信息
    device_info = data.device_info.model_dump() if data.device_info else None
    device = await get_or_create_device(user, request, device_info)

    # 创建包含设备信息的JWT Token
    from apps.auth.deps import create_access_token_with_device

    jwt_token = create_access_token_with_device(user, device)
    response.headers["Set-Cookie"] = "X-token=Bearer " + jwt_token

    return ApiResponse.success(
        data={
            "auth": {"id": user.id, "access_token": jwt_token, "refresh_token": jwt_token},
            "user": Smodel.model_to_dict(user, UserPersonalSchemaOut),
            "config": Smodel.model_to_dict(user.config, ConfigSchemaOut),
            "device": Smodel.model_to_dict(device, DeviceSchemaOut),
        },
    )


# 一键登录
@router.post(
    "/quick_login",
    summary="一键登录",
    description="手机号+验证码登录，无账号时自动注册",
    response_model=resmod(AuthSchemaResponse),
)
async def quick_login(data: QuickLoginSchema, response: Response, request: Request):
    """
    一键登录（有账号则登录，无账号则自动注册）
    :param data: 登录数据
    :param response: 响应对象
    :param request: 请求对象
    :return: jwt token和用户信息
    """
    try:
        # 调用一键登录核心逻辑
        user, is_new_user, device = await quick_login_or_register(
            mobile=data.mobile, captcha=data.captcha, request=request
        )

        # 创建包含设备信息的JWT Token
        jwt_token = create_access_token_with_device(user, device)
        response.headers["Set-Cookie"] = "X-token=Bearer " + jwt_token

        return ApiResponse.success(
            data={
                "auth": {"id": user.id, "access_token": jwt_token, "refresh_token": jwt_token},
                "user": Smodel.model_to_dict(user, UserPersonalSchemaOut),
                "config": Smodel.model_to_dict(user.config, ConfigSchemaOut),
                "device": Smodel.model_to_dict(device, DeviceSchemaOut),
                "is_new_user": is_new_user,
            },
        )
    except Exception as e:
        Slog.error(f"一键登录失败: {e}")
        raise Sexception(msg=str(e))


@router.put(
    "/update_password",
    summary="更改用户密码",
    response_model=BaseResponseModel,
)
# 更新密码, 需验证手机
async def update_password(data: UpdatePasswordSchema):
    check_captcha(data.mobile, data.captcha)  # 检查验证码
    # 转为小写
    # 手机验证码修改
    user = await User.get_or_none(mobile=data.mobile)
    if not user:
        raise Sexception(msg="密码修改失败")
    await user.update_from_dict({"password": en_password(data.password)}).save()
    user = await User.get_or_none(id=user.id).prefetch_related("config")
    # 修改密码
    return ApiResponse.success()

@router.put(
    "/update_mobile",
    summary="更改手机号",
    response_model=BaseResponseModel,
)
async def update_mobile(data: UpdateMobileSchema, response: Response):
    check_captcha(data.mobile, data.captcha)  # 检查验证码
    # 转为小写
    data.username = data.username.lower()
    # 修改手机号
    user = await User.get_or_none(username=data.username)
    if not user:
        raise Sexception(msg="手机号修改失败")
    await user.update_from_dict({"mobile": data.mobile}).save()
    return ApiResponse.success()


# 更改个人信息
@router.put(
    "/me",
    summary="更改个人信息",
    dependencies=[Security(check_permissions)],
    response_model=resmod(UserPersonalSchemaOut),
)
# 更改个人信息,
async def user_update(
    data: UserSchemaIn,
    user: User = Depends(get_auth_user),
):
    # 排除电话,密码和值为空的字段
    update_data = data.model_dump(exclude_unset=True, exclude={"mobile", "password"})
    data = await user.update_from_dict(update_data).save()
    obj = await User.get_or_none(id=user.id)
    return ApiResponse.success(data=obj)


# update one


# 获取个人信息
@router.get(
    "/me",
    summary="获取当前用户信息",
    dependencies=[Security(check_permissions)],
    response_model=resmod(UserPersonalSchemaOut),
)
# 当前登录用户信息
async def user_read_me(req: Request):
    """
    获取当前登陆用户信息
    :return:
    """
    user = await User.get_or_none(id=req.state.user_id).prefetch_related("config")
    return ApiResponse.success(data=user)


@router.put(
    "/avatar",
    dependencies=[Security(check_permissions)],
    response_model=resmod(UserPersonalSchemaOut),
)
async def avatar_update(req: Request, file: UploadFile = File(...)):  # noqa
    user: User = req.state.user
    file_url = await uploadFileFromUploadFile(file, "avatar")
    if user.avatar:
        Scos.delete(Surl.extract_path(user.avatar))
    user.avatar = file_url
    await user.save()
    obj = await User.get_or_none(id=user.id)
    return ApiResponse.success(data=obj)


# 获取用户公开信息
@router.get(
    "/user_public_info/{userId}",
    summary="获取用户公开信息",
    response_model=resmod(UserPublicSchemaOut),
)
async def get_user_pulic_info(userId: str):
    user = await User.get_or_none(id=userId)
    if not user:
        raise Sexception(msg="用户不存在")
    return ApiResponse.success(data=user)


# 获取公开信息
@router.get(
    "/users",
    response_model=resmod(UserPublicSchemaOut),
)
async def get_users():
    data = await User.all().select_related("role")
    return ApiResponse.success(data=data)


@router.get("/configs/me", dependencies=[Security(check_permissions)], response_model=resmod(ConfigSchemaOut))
async def configs_me(req: Request):
    config = await Config.get_or_none(user_id=req.state.user_id).prefetch_related(
        "current_study", "editing_book"
    )
    if not config:
        raise Sexception(msg="没有配置")
    if not config.editing_book:
        book = await Book.filter(user=req.state.user).first()
        if book:
            config.editing_book = book
            await config.save()
            Slog.debug(config.editing_book.id)
    return ApiResponse.success(data=config)


# 创建一个`update`的api, 用于更新我的`config`
@router.put("/configs/me", dependencies=[Security(check_permissions)], response_model=resmod(ConfigSchemaOut))
async def update_my_config(req: Request, post: ConfigSchemaIn):
    config = await Config.get_or_none(user_id=req.state.user_id)
    if not config:
        raise Sexception(msg="找不到配置")

    # 之前配置好的书籍,有可能已经被删除了,这里检查下,如果找不到相应的书籍, 就清空
    if post.current_study_id:
        current_study_book = await Book.get_or_none(id=post.current_study_id)
        if not current_study_book:
            post.current_study_id = None
    if post.editing_book_id:
        editing_book = await Book.get_or_none(id=post.editing_book_id)
        if not editing_book:
            post.editing_book_id = None

    # 排除掉`None`的字段
    config = await config.update_from_dict(post.model_dump(exclude_unset=True))
    await config.save()
    config = await Config.get_or_none(user_id=req.state.user_id).prefetch_related(
        "current_study", "editing_book"
    )
    # return ApiResponse.success(msg="更新成功")
    return ApiResponse.success(data=config)


# ==================== 设备管理相关接口 ====================


@router.get(
    "/devices/me", dependencies=[Security(check_permissions)], response_model=resmod(DeviceListSchemaOut)
)
async def get_my_devices(req: Request):
    """获取当前用户的所有设备"""
    devices = await Device.filter(user_id=req.state.user_id, is_active=True).order_by("-last_active")

    # 获取当前设备ID（从JWT中获取）
    current_device_id = getattr(req.state, "device_id", None)

    return ApiResponse.success(data={"devices": devices, "current_device_id": current_device_id})


@router.put(
    "/devices/{device_id}", dependencies=[Security(check_permissions)], response_model=resmod(DeviceSchemaOut)
)
async def update_device(device_id: str, req: Request, data: DeviceSchemaIn):
    """更新设备信息"""
    device = await Device.get_or_none(id=device_id, user_id=req.state.user_id)
    if not device:
        raise Sexception(msg="设备不存在")

    # 更新设备信息
    update_data = data.model_dump(exclude_unset=True)
    await device.update_from_dict(update_data)
    await device.save()

    return ApiResponse.success(data=device)


@router.delete(
    "/devices/{device_id}", dependencies=[Security(check_permissions)], response_model=BaseResponseModel
)
async def deactivate_device(device_id: str, req: Request):
    """停用设备（软删除）"""
    device = await Device.get_or_none(id=device_id, user_id=req.state.user_id)
    if not device:
        raise Sexception(msg="设备不存在")

    # 不能删除当前设备
    current_device_id = getattr(req.state, "device_id", None)
    if device_id == current_device_id:
        raise Sexception(msg="不能删除当前使用的设备")

    # 软删除设备
    device.is_active = False
    await device.save()

    return ApiResponse.success(msg="设备已停用")


@router.post(
    "/devices/register", dependencies=[Security(check_permissions)], response_model=resmod(DeviceSchemaOut)
)
async def register_device(req: Request, data: DeviceSchemaIn):
    """注册新设备"""
    user = await get_auth_user(req)

    # 获取设备信息
    device_info = data.model_dump(exclude_unset=True)
    device = await get_or_create_device(user, req, device_info)

    return ApiResponse.success(data=device)
