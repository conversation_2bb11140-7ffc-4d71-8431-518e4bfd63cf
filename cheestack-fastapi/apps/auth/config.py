from datetime import timedelta
from pydantic_settings import BaseSettings


class AuthConfig(BaseSettings):
    # JWT
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 365 * 24 * 60
    JWT_SECRET_KEY: str = "09d25e094baa6ca2556c818166b7a9563b83f7099f6f0f4caa6cf63b88e8d3e7"
    JWT_ALGORITHM: str = "HS256"
    JWT_EXP: int = 5  # minutes
    REFRESH_TOKEN_KEY: str = "09d25e094baa6ca2556c818166kdkdjsd9383f7099f6f0f4caa6cf63b88e8d3e7"
    REFRESH_TOKEN_EXP: timedelta = timedelta(days=365)
    SECURE_COOKIES: bool = True

    # session配置
    SESSION_SECRET_KEY: str = "session"
    SESSION_COOKIE: str = "session_id"
    # 过期时间, 单位为秒
    SESSION_MAX_AGE: int = 14 * 24 * 60 * 60
    # # tocken
    TOKEN_URL: str = "/api/v1/auth/login"


auth_settings = AuthConfig()
