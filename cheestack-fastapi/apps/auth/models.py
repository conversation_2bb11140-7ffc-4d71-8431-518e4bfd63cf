import shortuuid
from tortoise import fields
from core.fields import UrlCdnField
from core.models import TimeStampModelMixin


# 用户模型
class User(TimeStampModelMixin):
    role: fields.ManyToManyRelation["Role"] = fields.ManyToManyField(
        "models.Role", related_name="user", on_delete=fields.CASCADE
    )
    id = fields.CharField(
        max_length=100,
        pk=True,
        description="shortuuid",
        unique=True,
        default=shortuuid.uuid,
    )
    password = fields.CharField(100, description="用户密码")
    username = fields.Char<PERSON>ield(
        30,
        null=True,
        unique=True,
        description="用户名",
        # 移除严格的正则验证，允许在查询时使用手机号
        # validators=[RegexValidator(Spattern.username, 0)],
    )
    mobile = fields.CharField(100, null=True, unique=True, description="手机号码")
    email = fields.CharField(100, null=True, description="用户邮箱")
    avatar = UrlCdnField(200, null=True, description="用户头像")
    intro = fields.TextField(null=True, description="个人简介")
    is_superuser = fields.BooleanField(default=False, description="用户类型 True:超级管理员 False:普通管理员")
    status = fields.IntField(default=1, description="1正常,2禁用,3已删除,")

    class PydanticMeta:
        # 排除字段
        exclude = [
            "is_superuser",
            "status",
        ]

    class Meta:
        # 表名
        table = "user"
        # 表描述
        table_description: str = "用户表"
        # 默认排序
        ordering: list[str] = ["-created_at", "id"]

    # Defining ``__str__`` is optional, but gives you pretty
    # represent of model in debugger and interpreter
    # def __str__(self):
    #     return self.username if self.username else "UserModel"


# 角色模型
class Role(TimeStampModelMixin):
    # user: fields.ManyToManyRelation["User"]

    role_name = fields.CharField(max_length=15, description="角色名称")
    access: fields.ManyToManyRelation["Access"] = fields.ManyToManyField("models.Access", related_name="role")
    role_status = fields.BooleanField(description="True:启用 False:禁用")
    role_desc = fields.CharField(null=True, max_length=255, description="角色描述")

    class Meta:
        table_description = "角色表"
        table = "role"


# 权限
class Access(TimeStampModelMixin):
    role: fields.ManyToManyRelation[Role]
    access_name = fields.CharField(max_length=15, description="权限名称")
    parent_id = fields.IntField(default=0, description="父id")
    scopes = fields.CharField(unique=True, max_length=255, description="权限范围标识")
    access_desc = fields.CharField(null=True, max_length=255, description="权限描述")
    menu_icon = fields.CharField(null=True, max_length=255, description="菜单图标")
    is_check = fields.BooleanField(default=False, description="是否验证权限 True为验证 False不验证")
    is_menu = fields.BooleanField(default=False, description="是否为菜单 True菜单 False不是菜单")

    class Meta:
        table_description = "权限表"
        table = "access"


# 记录
class AccessLog(TimeStampModelMixin):
    # user_id = fields.CharField(max_length=32, description="用户ID")
    target_url = fields.CharField(null=True, description="访问的url", max_length=255)
    user_agent = fields.CharField(null=True, description="访问UA", max_length=255)
    request_params = fields.JSONField(null=True, description="请求参数get|post")
    ip = fields.CharField(null=True, max_length=32, description="访问IP")
    note = fields.CharField(null=True, max_length=255, description="备注")

    class Meta:
        table_description = "用户操作记录表"
        table = "access_log"


class SystemParams(TimeStampModelMixin):
    params_name = fields.CharField(unique=True, max_length=255, description="参数名")
    params = fields.JSONField(description="参数")

    class Meta:
        table = "system_params"
        table_description = "系统参数表"


class Config(TimeStampModelMixin):
    user = fields.OneToOneField("models.User", related_name="config", on_delete=fields.CASCADE)
    is_auto_play_audio = fields.BooleanField(description="是否自动播放音频", default=True, null=True)
    is_auto_play_ai_audio = fields.BooleanField(description="是否自动播放ai音频", default=True, null=True)
    review_number = fields.IntField(length=2, null=True, default=30)
    study_number = fields.IntField(length=2, null=True, default=10)
    editing_book = fields.ForeignKeyField(
        "models.Book", related_name="book_config", on_delete=fields.SET_NULL, null=True, default=None
    )
    current_study = fields.ForeignKeyField(
        "models.Book", related_name="book_current_study", on_delete=fields.SET_NULL, null=True, default=None
    )

    study_type = fields.IntField(
        default=0,
        description="当复习结果错误时, 0-维持当前stage,1-进入下一个stage,2-后退一个stage",
    )

    class Meta:
        table_description = "用户配置"
        table = "config"


# 设备管理模型
class Device(TimeStampModelMixin):
    id = fields.CharField(
        max_length=100,
        pk=True,
        description="设备ID",
        unique=True,
        default=shortuuid.uuid,
    )
    user = fields.ForeignKeyField("models.User", related_name="devices", on_delete=fields.CASCADE)
    device_name = fields.CharField(max_length=100, description="设备名称", null=True)
    device_type = fields.CharField(max_length=50, description="设备类型 (iOS/Android/Web)", null=True)
    device_model = fields.CharField(max_length=100, description="设备型号", null=True)
    os_version = fields.CharField(max_length=50, description="操作系统版本", null=True)
    app_version = fields.CharField(max_length=50, description="应用版本", null=True)
    device_token = fields.CharField(max_length=255, description="设备推送Token", null=True)
    user_agent = fields.CharField(max_length=500, description="用户代理", null=True)
    ip_address = fields.CharField(max_length=45, description="IP地址", null=True)
    last_active = fields.DatetimeField(description="最后活跃时间", auto_now=True)
    is_active = fields.BooleanField(default=True, description="是否活跃")
    login_count = fields.IntField(default=1, description="登录次数")

    class PydanticMeta:
        exclude = ["user_agent", "ip_address"]

    class Meta:
        table = "device"
        table_description = "用户设备表"
        ordering = ["-last_active", "id"]


# class Membership(Model):
#     user = fields.OneToOneField("models.User", related_name="membership", on_delete=fields.CASCADE)
#     level = fields.CharEnumField(MembershipLevel)
#     expired = fields.DatetimeField(null=True)
