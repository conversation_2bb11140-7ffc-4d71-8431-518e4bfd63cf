# 通用功能模块 (General)

> CheeStack 通用功能和系统管理模块

## 📖 功能概述

### 主要职责
通用功能模块提供应用的基础功能服务，包括版本管理、用户反馈、文件上传等通用性功能，为整个应用提供支撑服务。

- **版本管理**: 应用版本发布和更新检查
- **用户反馈**: 用户意见和问题反馈收集
- **文件上传**: 通用文件上传和管理服务
- **系统配置**: 应用配置参数管理

### 技术实现
- **文件存储**: 腾讯云COS对象存储
- **数据验证**: Pydantic模型验证
- **异步处理**: FastAPI异步文件上传
- **版本控制**: 语义化版本管理

## 🏗️ 架构设计

### 核心组件
```
apps/general/
├── __init__.py
├── apis.py                     # API路由定义
├── models.py                   # 数据模型 (Release, Feedback)
└── schema.py                   # Pydantic验证模型
```

### 数据模型
```python
# 版本发布模型
class Release:
    version: str                # 版本号 (如: 1.0.4)
    build_number: int           # 构建号 (如: 5)
    title: str                  # 版本标题
    description: str            # 版本描述
    download_url: str           # 下载链接
    is_force_update: bool       # 是否强制更新
    platform: str               # 平台 (android, ios, all)
    created_at: datetime        # 发布时间

# 用户反馈模型
class Feedback:
    user_id: str                # 用户ID
    type: str                   # 反馈类型 (bug, feature, other)
    title: str                  # 反馈标题
    content: str                # 反馈内容
    contact: str                # 联系方式
    device_info: dict           # 设备信息
    app_version: str            # 应用版本
    status: str                 # 处理状态 (pending, processing, resolved)
    created_at: datetime        # 提交时间
```

### 数据流
```
客户端请求 → 参数验证 → 业务处理 → 数据存储 → 响应返回
```

## 🔌 API接口

### 版本管理
- `GET /v1/general/release` - 获取最新版本信息
- `POST /v1/general/release` - 发布新版本 (管理员)
- `PUT /v1/general/release/{id}` - 更新版本信息 (管理员)

### 用户反馈
- `POST /v1/general/feedback` - 提交用户反馈
- `GET /v1/general/feedback` - 获取反馈列表 (管理员)
- `PUT /v1/general/feedback/{id}` - 更新反馈状态 (管理员)

### 文件上传
- `POST /v1/general/upload` - 通用文件上传
- `POST /v1/general/upload/image` - 图片上传
- `POST /v1/general/upload/audio` - 音频上传

### 请求示例

#### 获取版本信息
```python
GET /v1/general/release

# 响应
{
    "success": true,
    "data": {
        "version": "1.0.4",
        "build_number": 5,
        "title": "功能优化版本",
        "description": "修复已知问题，优化用户体验",
        "download_url": "https://example.com/app-v1.0.4.apk",
        "is_force_update": false,
        "platform": "android",
        "created_at": "2024-07-16T10:00:00Z"
    }
}
```

#### 提交用户反馈
```python
POST /v1/general/feedback
{
    "type": "bug",
    "title": "语音识别异常",
    "content": "在使用语音识别功能时，应用偶尔会崩溃",
    "contact": "<EMAIL>",
    "device_info": {
        "platform": "android",
        "version": "12",
        "model": "Pixel 6",
        "app_version": "1.0.4"
    }
}

# 响应
{
    "success": true,
    "data": {
        "id": "feedback-id",
        "status": "pending",
        "created_at": "2024-07-16T10:00:00Z"
    },
    "message": "反馈提交成功，我们会尽快处理"
}
```

#### 文件上传
```python
POST /v1/general/upload
Content-Type: multipart/form-data

file: <binary-data>
type: "image"  # image, audio, video, document

# 响应
{
    "success": true,
    "data": {
        "url": "https://cdn.example.com/files/xxx.jpg",
        "filename": "xxx.jpg",
        "size": 1024000,
        "type": "image",
        "upload_time": "2024-07-16T10:00:00Z"
    }
}
```

## 🔧 核心功能

### 版本检查逻辑
```python
async def check_app_update(current_version: str, platform: str):
    """检查应用更新"""
    latest_release = await Release.filter(
        platform__in=[platform, "all"]
    ).order_by("-created_at").first()
    
    if not latest_release:
        return {"has_update": False}
    
    # 版本比较逻辑
    if compare_version(current_version, latest_release.version) < 0:
        return {
            "has_update": True,
            "latest_version": latest_release.version,
            "download_url": latest_release.download_url,
            "is_force_update": latest_release.is_force_update,
            "description": latest_release.description
        }
    
    return {"has_update": False}
```

### 文件上传处理
```python
from apps.tcyun.utils import Scos

async def upload_file(file: UploadFile, file_type: str):
    """通用文件上传"""
    # 文件类型验证
    allowed_types = {
        "image": [".jpg", ".jpeg", ".png", ".gif"],
        "audio": [".mp3", ".wav", ".m4a"],
        "video": [".mp4", ".mov"],
        "document": [".pdf", ".doc", ".docx"]
    }
    
    file_ext = os.path.splitext(file.filename)[1].lower()
    if file_ext not in allowed_types.get(file_type, []):
        raise Sexception(msg="不支持的文件类型")
    
    # 文件大小验证
    max_sizes = {
        "image": 5 * 1024 * 1024,    # 5MB
        "audio": 10 * 1024 * 1024,   # 10MB
        "video": 50 * 1024 * 1024,   # 50MB
        "document": 10 * 1024 * 1024  # 10MB
    }
    
    if file.size > max_sizes.get(file_type, 5 * 1024 * 1024):
        raise Sexception(msg="文件大小超出限制")
    
    # 上传到腾讯云COS
    cos_client = Scos()
    file_url = await cos_client.upload_file(file, file_type)
    
    return {
        "url": file_url,
        "filename": file.filename,
        "size": file.size,
        "type": file_type,
        "upload_time": datetime.utcnow()
    }
```

### 反馈处理流程
```python
async def process_feedback(feedback_data: dict, user_id: str):
    """处理用户反馈"""
    # 创建反馈记录
    feedback = await Feedback.create(
        user_id=user_id,
        type=feedback_data["type"],
        title=feedback_data["title"],
        content=feedback_data["content"],
        contact=feedback_data.get("contact"),
        device_info=feedback_data.get("device_info", {}),
        app_version=feedback_data.get("app_version"),
        status="pending"
    )
    
    # 发送通知给管理员 (可选)
    await notify_admin_new_feedback(feedback)
    
    return feedback
```

## 🧪 测试

### 单元测试
```python
@pytest.mark.asyncio
async def test_get_latest_release():
    """测试获取最新版本"""
    response = await client.get("/v1/general/release")
    assert response.status_code == 200
    data = response.json()["data"]
    assert "version" in data
    assert "download_url" in data

@pytest.mark.asyncio
async def test_submit_feedback():
    """测试提交反馈"""
    feedback_data = {
        "type": "bug",
        "title": "测试反馈",
        "content": "这是一个测试反馈",
        "contact": "<EMAIL>"
    }
    response = await client.post("/v1/general/feedback", json=feedback_data)
    assert response.status_code == 200
    assert response.json()["success"] is True

@pytest.mark.asyncio
async def test_file_upload():
    """测试文件上传"""
    with open("test_image.jpg", "rb") as f:
        files = {"file": ("test.jpg", f, "image/jpeg")}
        data = {"type": "image"}
        response = await client.post("/v1/general/upload", files=files, data=data)
    assert response.status_code == 200
    assert "url" in response.json()["data"]
```

### 集成测试
- 完整的版本检查和更新流程测试
- 文件上传和存储验证测试
- 反馈提交和处理流程测试

## 📊 数据统计

### 反馈统计
```python
async def get_feedback_stats():
    """获取反馈统计信息"""
    total_count = await Feedback.all().count()
    pending_count = await Feedback.filter(status="pending").count()
    resolved_count = await Feedback.filter(status="resolved").count()
    
    type_stats = await Feedback.annotate(
        count=Count("id")
    ).group_by("type").values("type", "count")
    
    return {
        "total": total_count,
        "pending": pending_count,
        "resolved": resolved_count,
        "by_type": type_stats
    }
```

### 版本使用统计
```python
async def get_version_usage_stats():
    """获取版本使用统计"""
    # 从用户登录日志中统计版本使用情况
    # 这里需要结合用户认证模块的数据
    pass
```

## 🔧 配置说明

### 环境变量
```env
# 腾讯云COS配置
TENCENT_SECRET_ID=your-secret-id
TENCENT_SECRET_KEY=your-secret-key
COS_BUCKET=your-bucket
COS_REGION=ap-beijing

# 文件上传配置
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp3,wav,mp4

# 反馈通知配置
ADMIN_EMAIL=<EMAIL>
FEEDBACK_WEBHOOK_URL=https://your-webhook-url
```

### 数据库配置
```python
# 反馈表索引
CREATE INDEX idx_feedback_user_id ON feedback(user_id);
CREATE INDEX idx_feedback_type ON feedback(type);
CREATE INDEX idx_feedback_status ON feedback(status);
CREATE INDEX idx_feedback_created_at ON feedback(created_at);

# 版本表索引
CREATE INDEX idx_release_platform ON release(platform);
CREATE INDEX idx_release_created_at ON release(created_at);
```

## 📝 开发注意事项

### 安全考虑
- 文件上传需要严格的类型和大小验证
- 防止恶意文件上传和存储
- 用户反馈内容需要过滤敏感信息
- 版本发布权限严格控制

### 性能优化
- 文件上传使用异步处理
- 大文件上传支持分片上传
- 反馈列表支持分页查询
- 版本信息可以缓存

### 常见问题
- **文件上传失败**: 检查COS配置和网络连接
- **版本检查异常**: 确认版本号格式和比较逻辑
- **反馈提交失败**: 检查数据验证和数据库连接
- **权限不足**: 确认管理员权限配置

## 🔗 依赖关系
- **内部依赖**: apps.auth (用户认证), apps.tcyun (文件存储)
- **外部依赖**: fastapi, tortoise-orm, pydantic
- **服务依赖**: 腾讯云COS, PostgreSQL数据库

---

**注意**: 通用功能模块为整个应用提供基础服务，修改时需要考虑对其他模块的影响。
