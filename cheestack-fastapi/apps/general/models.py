from datetime import datetime
from email.policy import default
from tortoise import Model
from apps.general.enum import FeedbackType, FeedbackStatusType
from core.fields import UrlCdnField
from tortoise import fields

from core.models import TimeStampModelMixin


# class AppVersion(TimeStampModelMixin):
#     id = fields.IntField(pk=True)
#     version_name = fields.CharField(max_length=255)
#     version_code = fields.IntField()
#     is_force_update = fields.BooleanField(default=False)
#     release_date = fields.DatetimeField()
#     download_url = UrlCdnField(max_length=255)
#     changelog = fields.TextField()


class Release(Model):
    version = fields.CharField(max_length=255)
    min_version = fields.CharField(max_length=255)
    build = fields.IntField()
    min_build = fields.IntField()
    apk_url = UrlCdnField(max_length=255, null=True)
    appstore_id = fields.CharField(max_length=255)
    changelog = fields.TextField(null=True)
    release_time = fields.DatetimeField(default=datetime.now, null=True)


class Feedback(TimeStampModelMixin):
    type = fields.CharEnumField(enum_type=FeedbackType, default=FeedbackType.SUGGESTION)
    content = fields.TextField(null=True)
    status = fields.CharEnumField(enum_type=FeedbackStatusType, default=FeedbackStatusType.SUBMITTED)
    book = fields.ForeignKeyField(
        "models.Book", related_name="feedbacks", null=True, on_delete=fields.CASCADE
    )
    card = fields.ForeignKeyField(
        "models.Card", related_name="feedbacks", null=True, on_delete=fields.CASCADE
    )
    user = fields.ForeignKeyField("models.User", related_name="feedbacks")


class FeedbackComment(TimeStampModelMixin):
    content = fields.TextField(null=True)
    feedback = fields.ForeignKeyField("models.Feedback", related_name="comments", on_delete=fields.CASCADE)
    user = fields.ForeignKeyField("models.User", related_name="comments", on_delete=fields.CASCADE)

    class Meta:
        table = "feedback_comment"
