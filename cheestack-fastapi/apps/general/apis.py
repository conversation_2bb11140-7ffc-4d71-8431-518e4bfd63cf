from typing import List
from fastapi.responses import HTMLResponse
from apps.auth.deps import check_permissions
from apps.general.models import Feedback, Release
from apps.general.schema import FeedbackSchemaIn, FeedbackSchemaOut, ReleaseSchema
from core.exceptions import Sexception
from core.responses import ApiResponse
from fastapi import APIRouter, Request, Security
from core.schema import BaseResponseModel, resmod
from core.config import settings
from sutils.filters import Squery

router = APIRouter(prefix="/v1/general")


@router.get(
    "/release",
    response_model=resmod(ReleaseSchema),
)
async def get_app_release():
    obj = await Release.all().order_by("-id").first()
    if not obj:
        raise Sexception(msg="没有发布版本")
    return ApiResponse.success(data=obj)


@router.get("/healthz")
async def index():
    return ApiResponse.success()


@router.get("/support", response_class=HTMLResponse)
async def get_support(req: Request):
    return settings.TEMPLATES.TemplateResponse("support.html", {"request": req})


@router.get("/privacy", response_class=HTMLResponse)
async def get_privacy(req: Request):
    return settings.TEMPLATES.TemplateResponse("privacy.html", {"request": req})

@router.get("/agreement", response_class=HTMLResponse)
async def get_privacy(req: Request):
    return settings.TEMPLATES.TemplateResponse("agreement.html", {"request": req})


@router.post(
    "/feedbacks",
    dependencies=[Security(check_permissions)],
    response_model=BaseResponseModel,
)
async def feedbacks_create(req: Request, data: FeedbackSchemaIn):
    obj = await Feedback.create(
        **data.model_dump(exclude_unset=True, exclude_defaults=True),
        user_id=req.state.user_id,
    )
    if not obj:
        raise Sexception(msg="创建失败")
    return ApiResponse.success()


@router.get(
    "/feedbacks",
    dependencies=[Security(check_permissions)],
    response_model=resmod(List[FeedbackSchemaOut]),
)
async def feedbacks_list(
    req: Request, skip: int = 0, limit: int = 20, filters: str = None, order: str | None = "-id"
):
    queryset = Feedback.all().prefetch_related("user", "comments")
    if filters:
        query = Squery.dict_to_query(filters=filters)
        queryset = queryset.filter(query)
    limit = limit if limit < 50 else 50
    order: List[str] = order.split(",") if order else "-created_at"
    objs = await queryset.offset(skip).limit(limit).order_by(*order)
    return ApiResponse.success(data=objs)
