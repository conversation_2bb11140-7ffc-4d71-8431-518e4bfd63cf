from datetime import datetime
from typing import List
from pydantic import BaseModel


class ReleaseSchema(BaseModel):
    version: str
    min_version: str
    build: int
    min_build: int
    apk_url: str
    appstore_id: str
    changelog: str
    release_time: datetime

    class Config:
        arbitrary_types_allowed = True


class FeedbackCommentSchemaIn(BaseModel):
    content: str


class FeedbackCommentSchemaOut(BaseModel):
    id: int
    user_id: str
    content: str
    created_at: datetime


class FeedbackSchemaIn(BaseModel):
    book_id: int | None = None
    card_id: int | None = None
    content: str
    comments: list[str] = []

    class Config:
        arbitrary_types_allowed = True


class FeedbackSchemaOut(BaseModel):
    id: int
    user_id: str
    content: str
    created_at: datetime
    updated_at: datetime
    comments: List[FeedbackCommentSchemaOut] = []

    class Config:
        arbitrary_types_allowed = True
