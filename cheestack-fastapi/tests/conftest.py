"""
测试配置文件
提供测试所需的fixtures和配置
"""

import asyncio
import os
from typing import AsyncGenerator, Generator

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from tortoise import Tortoise

from main import app


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """创建事件循环用于测试"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session")
async def init_db() -> AsyncGenerator[None, None]:
    """初始化测试数据库"""
    # 使用内存SQLite数据库进行测试
    await Tortoise.init(
        db_url="sqlite://:memory:",
        modules={"models": ["core.models", "apps.auth.models", "apps.study.models"]},
    )
    await Tortoise.generate_schemas()
    yield
    await Tortoise.close_connections()


@pytest.fixture
def client() -> TestClient:
    """创建测试客户端"""
    return TestClient(app)


@pytest_asyncio.fixture
async def async_client(init_db) -> AsyncGenerator[AsyncClient, None]:
    """创建异步测试客户端"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def test_user_data() -> dict:
    """测试用户数据"""
    return {
        "mobile": "13800138000",
        "password": "test123456",
        "nickname": "测试用户",
        "email": "<EMAIL>"
    }


@pytest.fixture
def test_book_data() -> dict:
    """测试书籍数据"""
    return {
        "title": "测试书籍",
        "description": "这是一本测试书籍",
        "cover_url": "https://example.com/cover.jpg",
        "category": "语言学习"
    }


@pytest.fixture
def test_card_data() -> dict:
    """测试卡片数据"""
    return {
        "question": "What is Python?",
        "answer": "Python is a programming language",
        "question_audio_url": "",
        "answer_audio_url": "",
        "question_image_url": "",
        "answer_image_url": "",
        "tags": ["programming", "python"]
    }


@pytest.fixture
def auth_headers(test_user_data) -> dict:
    """认证头信息"""
    # 这里应该返回实际的JWT token
    # 在实际测试中需要先创建用户并获取token
    return {"Authorization": "Bearer test_token"}