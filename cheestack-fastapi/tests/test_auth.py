"""
用户认证相关测试
"""

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient


class TestAuth:
    """用户认证测试类"""

    def test_register_success(self, client: TestClient, test_user_data: dict):
        """测试用户注册成功"""
        response = client.post("/api/v1/auth/register", json=test_user_data)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "data" in data
        assert "access_token" in data["data"]

    def test_register_duplicate_mobile(self, client: TestClient, test_user_data: dict):
        """测试重复手机号注册"""
        # 先注册一次
        client.post("/api/v1/auth/register", json=test_user_data)
        # 再次注册相同手机号
        response = client.post("/api/v1/auth/register", json=test_user_data)
        assert response.status_code == 400
        data = response.json()
        assert data["code"] == 400
        assert "已存在" in data["message"]

    def test_register_invalid_mobile(self, client: TestClient):
        """测试无效手机号注册"""
        invalid_data = {
            "mobile": "invalid_mobile",
            "password": "test123456",
            "nickname": "测试用户"
        }
        response = client.post("/api/v1/auth/register", json=invalid_data)
        assert response.status_code == 422

    def test_login_success(self, client: TestClient, test_user_data: dict):
        """测试用户登录成功"""
        # 先注册用户
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # 登录
        login_data = {
            "mobile": test_user_data["mobile"],
            "password": test_user_data["password"]
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "access_token" in data["data"]

    def test_login_wrong_password(self, client: TestClient, test_user_data: dict):
        """测试错误密码登录"""
        # 先注册用户
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # 使用错误密码登录
        login_data = {
            "mobile": test_user_data["mobile"],
            "password": "wrong_password"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 401
        data = response.json()
        assert data["code"] == 401

    def test_login_nonexistent_user(self, client: TestClient):
        """测试不存在的用户登录"""
        login_data = {
            "mobile": "13900139000",
            "password": "test123456"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 404
        data = response.json()
        assert data["code"] == 404

    @pytest.mark.asyncio
    async def test_get_current_user(self, async_client: AsyncClient, auth_headers: dict):
        """测试获取当前用户信息"""
        response = await async_client.get("/api/v1/auth/me", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "data" in data
        assert "mobile" in data["data"]

    @pytest.mark.asyncio
    async def test_get_current_user_unauthorized(self, async_client: AsyncClient):
        """测试未授权获取用户信息"""
        response = await async_client.get("/api/v1/auth/me")
        assert response.status_code == 401

    def test_refresh_token(self, client: TestClient, test_user_data: dict):
        """测试刷新token"""
        # 先注册并登录
        client.post("/api/v1/auth/register", json=test_user_data)
        login_response = client.post("/api/v1/auth/login", json={
            "mobile": test_user_data["mobile"],
            "password": test_user_data["password"]
        })
        token = login_response.json()["data"]["access_token"]
        
        # 刷新token
        headers = {"Authorization": f"Bearer {token}"}
        response = client.post("/api/v1/auth/refresh", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "access_token" in data["data"]

    def test_logout(self, client: TestClient, test_user_data: dict):
        """测试用户登出"""
        # 先注册并登录
        client.post("/api/v1/auth/register", json=test_user_data)
        login_response = client.post("/api/v1/auth/login", json={
            "mobile": test_user_data["mobile"],
            "password": test_user_data["password"]
        })
        token = login_response.json()["data"]["access_token"]
        
        # 登出
        headers = {"Authorization": f"Bearer {token}"}
        response = client.post("/api/v1/auth/logout", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200