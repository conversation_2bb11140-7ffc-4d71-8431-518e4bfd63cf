"""
书籍管理相关测试
"""

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient


class TestBooks:
    """书籍管理测试类"""

    @pytest.mark.asyncio
    async def test_create_book_success(
        self, async_client: AsyncClient, auth_headers: dict, test_book_data: dict
    ):
        """测试创建书籍成功"""
        response = await async_client.post("/v1/books", json=test_book_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "data" in data
        assert data["data"]["title"] == test_book_data["title"]

    @pytest.mark.asyncio
    async def test_create_book_unauthorized(
        self, async_client: AsyncClient, test_book_data: dict
    ):
        """测试未授权创建书籍"""
        response = await async_client.post("/v1/books", json=test_book_data)
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_get_books_list(self, async_client: AsyncClient, auth_headers: dict):
        """测试获取书籍列表"""
        response = await async_client.get("/v1/books", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "data" in data
        assert isinstance(data["data"], list)

    @pytest.mark.asyncio
    async def test_get_books_with_pagination(
        self, async_client: AsyncClient, auth_headers: dict
    ):
        """测试分页获取书籍列表"""
        params = {"page": 1, "size": 10}
        response = await async_client.get("/v1/books", params=params, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "data" in data

    @pytest.mark.asyncio
    async def test_get_book_detail(self, async_client: AsyncClient, auth_headers: dict):
        """测试获取书籍详情"""
        # 先创建一本书
        book_data = {
            "title": "测试书籍详情",
            "description": "测试描述",
            "category": "测试分类"
        }
        create_response = await async_client.post("/v1/books", json=book_data, headers=auth_headers)
        book_id = create_response.json()["data"]["id"]

        # 获取书籍详情
        response = await async_client.get(f"/v1/books/{book_id}", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["title"] == book_data["title"]

    @pytest.mark.asyncio
    async def test_get_nonexistent_book(
        self, async_client: AsyncClient, auth_headers: dict
    ):
        """测试获取不存在的书籍"""
        response = await async_client.get("/v1/books/999999", headers=auth_headers)
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_book(self, async_client: AsyncClient, auth_headers: dict):
        """测试更新书籍"""
        # 先创建一本书
        book_data = {
            "title": "原始标题",
            "description": "原始描述",
            "category": "原始分类"
        }
        create_response = await async_client.post("/v1/books", json=book_data, headers=auth_headers)
        book_id = create_response.json()["data"]["id"]

        # 更新书籍
        update_data = {
            "title": "更新后的标题",
            "description": "更新后的描述"
        }
        response = await async_client.put(f"/v1/books/{book_id}", json=update_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["title"] == update_data["title"]

    @pytest.mark.asyncio
    async def test_delete_book(self, async_client: AsyncClient, auth_headers: dict):
        """测试删除书籍"""
        # 先创建一本书
        book_data = {
            "title": "待删除的书籍",
            "description": "这本书将被删除",
            "category": "测试分类"
        }
        create_response = await async_client.post("/v1/books", json=book_data, headers=auth_headers)
        book_id = create_response.json()["data"]["id"]

        # 删除书籍
        response = await async_client.delete(f"/v1/books/{book_id}", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200

        # 验证书籍已被删除
        get_response = await async_client.get(f"/v1/books/{book_id}", headers=auth_headers)
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_search_books(self, async_client: AsyncClient, auth_headers: dict):
        """测试搜索书籍"""
        # 先创建几本书
        books = [
            {"title": "Python编程", "description": "学习Python", "category": "编程"},
            {"title": "Java基础", "description": "Java入门", "category": "编程"},
            {"title": "英语学习", "description": "英语教程", "category": "语言"}
        ]

        for book in books:
            await async_client.post("/v1/books", json=book, headers=auth_headers)

        # 搜索包含"Python"的书籍
        params = {"search": "Python"}
        response = await async_client.get("/v1/books", params=params, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        # 应该找到包含Python的书籍
        found_python = any("Python" in book["title"] for book in data["data"])
        assert found_python

    @pytest.mark.asyncio
    async def test_filter_books_by_category(
        self, async_client: AsyncClient, auth_headers: dict
    ):
        """测试按分类筛选书籍"""
        params = {"category": "编程"}
        response = await async_client.get("/v1/books", params=params, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
