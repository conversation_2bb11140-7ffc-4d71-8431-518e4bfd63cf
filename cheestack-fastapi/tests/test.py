import re


def remove_punctuation(text: str) -> str:
    """删除字符串中的标点符号，包括中英文标点符号"""
    # 此处没有不会删除`'`字符, 避免英文句子不完整
    punctuation = r"""!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~。，、；：“”‘’？！（）【】"""
    # punctuation = r"""!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~。，、；：“”‘’？！（）【】"""
    return re.sub(f"[{re.escape(punctuation)}]", "", text)


# 示例用法
sample_text_1 = "Hell'o, world!"
sample_text_2 = "你好，世界。"

print(remove_punctuation(sample_text_1))  # 输出: "Hello world"
print(remove_punctuation(sample_text_2))  # 输出: "你好世界"
