import os
from typing import Optional


class Sfile:
    def __init__(self, filename):
        pass

    @staticmethod
    def get_file_paths(directory: str, suffix: str = None, include_subdir: bool = True):
        """获取指定目录下的所有文件
        @param directory: 目录路径
        @param suffix: 文件后缀
        @return: 文件路径列表,含`directory`目录
        """
        file_list = []
        if suffix is None:
            return [
                os.path.join(directory, f)
                for f in os.listdir(directory)
                if os.path.isfile(os.path.join(directory, f))
            ]
        # 使用glob模块匹配目录下所有的PDF文件
        # pdf_files.extend(glob.glob(os.path.join(directory, "*.pdf")))
        # 如果需要递归地搜索子目录下的PDF文件，可以使用os.walk函数
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith(suffix):
                    file_list.append(os.path.join(root, file))
        return file_list

    @staticmethod
    def mkdirs(path):
        # 如果路径不存在，则创建目录
        if not os.path.exists(path):
            os.makedirs(path)
        else:
            pass

    @staticmethod
    def get_file_type(filename: str) -> Optional[str]:
        """
        根据文件名判断文件类型。
        :param filename: 文件名（包括扩展名）
        :return: 文件类型 ('image', 'audio', 'video', 'file')
        """
        image_extensions = {
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".bmp",
            ".tiff",
            ".webp",
            ".svg",
            ".ico",
            ".heic",
        }
        audio_extensions = {".mp3", ".wav", ".aac", ".flac", ".ogg", ".m4a", ".wma", ".alac", ".aiff", ".amr"}
        video_extensions = {
            ".mp4",
            ".avi",
            ".mov",
            ".mkv",
            ".flv",
            ".wmv",
            ".webm",
            ".mpeg",
            ".mpg",
            ".3gp",
            ".ogv",
            ".m4v",
        }

        # 获取文件扩展名，并转换为小写以进行匹配
        ext = filename.lower().rsplit(".", 1)[-1]

        if f".{ext}" in image_extensions:
            return "image"
        elif f".{ext}" in audio_extensions:
            return "audio"
        elif f".{ext}" in video_extensions:
            return "video"
        else:
            return "file"
