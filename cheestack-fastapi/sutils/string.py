from datetime import datetime
import hashlib
import os
import random
import re
from typing import List
import uuid
import difflib
from sutils.log import Slog


class Sstring:
    def __init__(self):
        pass

    @staticmethod
    def insert_string_at_position(original_string: str, string_to_insert: str, index: int = 0):
        """
        在字符串的指定位置插入字符

        Parameters:
        - original_string: 原始字符串
        - char_to_insert: 要插入的字符
        - position: 插入的位置索引（从0开始）

        Returns:
        - modified_string: 修改后的字符串
        """
        return original_string[:index] + string_to_insert + original_string[index:]

    def replace_punctuation_from_string(text):
        """
        将字符串中的常见标点符号替换为下划线，并去除尾部的标点符号

        Parameters:
        - text (str): 待处理的字符串

        Returns:
        - str: 处理后的字符串
        """
        # 如果text为path
        # 常见的标点符号正则表达式
        punctuation_pattern = r"[!\"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~，。；：、？！\n]"
        # 将常见标点符号替换为下划线
        text = re.sub(punctuation_pattern, "_", text)
        Slog.debug(f"text:{text}")
        # 返回前10个字符
        result = text[:10]
        # 去除尾部的标点符号
        result = result.rstrip("_")
        return result

    def replace_punctuation_from_filename(filename: str):
        """
        将字符串中的常见标点符号替换为下划线，并去除尾部的标点符号

        Parameters:
        - filename (str): 待处理的字符串

        Returns:
        - str: 处理后的字符串
        """

        # 分成名字和后缀
        name, suffix = os.path.splitext(filename)

        # 常见的标点符号正则表达式
        punctuation_pattern = r"[!！?？,.，。;；:：、#@&*()_+{}|\"'<>~`\\[\\]\\/ ]"
        # 将常见标点符号替换为下划线
        text = re.sub(punctuation_pattern, "_", name, flags=re.UNICODE)
        Slog.debug(f"text111:{text}")
        # 去除连续的下划线
        text = re.sub(r'_+', '_', text)
        # 去除尾部的下划线
        text = text.rstrip("_")
        return text

    @staticmethod
    def utc_time_str_to_datetime(utc_time_str: str):
        """
        @utc_time_str: 须为iso8601格式的字符串
        """
        try:
            # 如果转换失败，使用ISO格式转换
            datetime_obj: datetime = datetime.fromisoformat(str(utc_time_str))
        except ValueError:
            try:
                # "2023-04-26T09:57:00.602801+00:00",
                # 如果您正在使用HTTP请求发送带有路径参数的URL，
                # 那么 + 符号有可能被编码为URL编码中的空格字符 %20，
                # 这里需要将空格字符替换为 + 符号
                datetime_obj = datetime.fromisoformat(
                    utc_time_str.replace(" ", "+")
                    # 或者更改为以下代码
                    # utc_time_str.replace(" 00:00", "Z")
                )
            except Exception:
                # 如果三种方法都无法转换，抛出异常
                return None
        return datetime_obj

    @staticmethod
    def is_chinese(char: str) -> bool:
        """判断字符是否为中文"""
        return "\u4e00" <= char <= "\u9fff"

    @staticmethod
    def is_english(char: str) -> bool:
        """判断字符是否为英文"""
        return "a" <= char.lower() <= "z"

    @staticmethod
    def detect_language(text: str) -> str:
        """检测字符串的主要语言"""
        text = text.strip()
        if not text:
            return "Unknown"
        has_chinese = any(Sstring.is_chinese(char) for char in text)
        has_english = any(Sstring.is_english(char) for char in text)
        if has_chinese:
            return "zh"
        elif has_english:
            return "en"
        else:
            return "en"

    def unique_str() -> str:
        """
        返回唯一随机字符串
        :return: str
        """
        only = hashlib.md5(str(uuid.uuid1()).encode(encoding="UTF-8")).hexdigest()
        return str(only)

    # 创建一个函数, 用于将传入的字符串参数进行哈希加密, 并返回加密后的字符串
    def hash_str(s: str) -> str:
        """
        加密字符串
        :param s: str
        :return: str 返回加密后的字符串
        """
        return hashlib.md5(s.encode(encoding="UTF-8")).hexdigest()

    def code_number(ln: int) -> str:
        """
        返回随机数字
        :param ln: 长度
        :return: str
        """
        code = ""
        for i in range(ln):
            ch = chr(random.randrange(ord("0"), ord("9") + 1))
            code += ch

        return code

    def insert_string_after_substring(src_string, insert_string, substring):
        """
        在字符串的指定子串后面添加后缀

        Parameters:
        - src_string (str): 原始字符串
        - insert_string (str): 要添加的字符串
        - substring (str): 要在其后面添加后缀的子串, 如果未找到子串，则直接返回原始字符串

        Returns:
        - str: 添加后缀后的字符串
        """
        # 在指定子串后面添加后缀
        index = src_string.find(substring)
        if index == -1:
            return src_string + insert_string  # 如果未找到子串，则直接在最后添加
        modified_string = (
            src_string[: index + len(substring)] + insert_string + src_string[index + len(substring) :]
        )
        return modified_string

    @staticmethod
    def remove_punctuation(text: str) -> str:
        """删除字符串中的标点符号，包括中英文标点符号"""
        # 此处没有不会删除`'`符, 避免英文句子不完整
        punctuation = r"""!"#$%&()*+,-./:;<=>?@[\]^_`{|}~。，、；：“”‘’？！（）【】"""
        # punctuation = r"""!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~。，、；：“”‘’？！（）【】"""
        return re.sub(f"[{re.escape(punctuation)}]", "", text)

    @staticmethod
    def split_sentence_by_punctuation(text: str):
        """根据标点符号和空格拆分字符串"""
        punctuation = r""" !"#$%&()*+,-./:;<=>?@[\]^_`{|}~。，、；：“”‘’？！（）【】"""
        return [
            segment.lower() for segment in re.split(f"[{re.escape(punctuation)}]", text) if segment.strip()
        ]

    @staticmethod
    def list_word_to_sentence(words: list[str]) -> str:
        # 连接所有部分
        full_sentence = " ".join(words)
        # 删除多余的空格
        full_sentence = " ".join(full_sentence.split())
        # 确保句子首字母大写
        full_sentence = full_sentence.capitalize()
        # 如果句子末尾没有标点符号，添加句号
        if full_sentence[-1] not in ".!?":
            full_sentence += "."

        return full_sentence

    @staticmethod
    def compare_similarity(stander_text, target_text):
        """比较两个字符串的相似度
        @param stander_text: 标准字符串
        @param target_text: 目标字符串
        @return: similarity, unmatch: 相似度, 不匹配的列表
        """
        lang = Sstring.detect_language(stander_text)

        if lang == "en":
            stander_text = Sstring.split_sentence_by_punctuation(stander_text.lower())
            target_text = Sstring.split_sentence_by_punctuation(target_text.lower())
        elif lang == "cn":
            stander_text = re.findall(r"[\u4e00-\u9fff]", stander_text)
            target_text = re.findall(r"[\u4e00-\u9fff]", target_text)
        # 创建 SequenceMatcher 对象
        matcher = difflib.SequenceMatcher(None, stander_text, target_text)
        # 获取差异值
        # diff [('replace', 0, 1, 0, 3), ('equal', 1, 7, 3, 9), ('insert', 7, 7, 9, 12), ('equal', 7, 10, 12, 15)]
        # 每个元组代表一个操作,描述了如何将第一个序列（在这里可以视为标准文本）转换为第二个序列（目标文本）
        diff = list(matcher.get_opcodes())
        # 计算相似度(0~1)
        similarity = matcher.ratio()
        # 定义不匹配的列表
        unmatch = []
        # 遍历并将不匹配的元素添加到列表中
        for tag, i1, i2, j1, j2 in diff:
            if tag == "equal":
                # print("equal", stander_text[i1:i2], target_text[j1:j2])
                pass
            elif tag == "replace":
                # print("replace", stander_text[i1:i2], target_text[j1:j2])
                unmatch.extend(stander_text[i1:i2])
            elif tag == "delete":
                # print("delete", stander_text[i1:i2], target_text[j1:j2])
                unmatch.extend(stander_text[i1:i2])
                # print(stander_text[i1:i2], target_text[j1:j2])
            elif tag == "insert":
                # print("insert", stander_text[i1:i2], target_text[j1:j2])
                pass
        return similarity, unmatch

    @staticmethod
    def split_sentence_to_word_list(data: List[str]) -> List[str]:
        """
        将句子列表拆分为单词列表并去重, 仅保留英文
        @data: 句子列表
        @return: 单词列表
        """
        result = []

        # 定义正则表达式，移除标点符号
        punctuation_pattern = r"[^\w\s\u4e00-\u9fff]"

        for item in data:
            # 1. 去除标点符号
            cleaned_item = re.sub(punctuation_pattern, "", item)

            # 2. 使用则表达式分离中英文和数字
            # 这个正则表达式匹配单个中文字符、连续英文单词或连续数字
            parts = re.findall(r"[a-zA-Z]+", cleaned_item)

            for part in parts:
                # 3. 判断类型并添加到结果列表
                if re.match(r"[a-zA-Z]+", part):
                    # 英文单词, 转为小写
                    result.append(part.lower())

        # 4. 去重处理并���回列表
        return list(set(result))

    @staticmethod
    def remove_punctuation_for_tts(text):
        """
        移除字符串中的标点符号
        Parameters:
        - text (str): 待处理的字符串

        Returns:
        - str: 处理后的字符串
        """
        # 常见的标点符号正则表达式
        punctuation_pattern = r"[#$%&*+<=>@[\\\]_~]"  # 删除不会被发音的标点符号
        # 移除标点符号
        text = re.sub(punctuation_pattern, "", text)
        return text
