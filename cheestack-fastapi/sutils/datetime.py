import datetime
import pytz
import shortuuid


class Sdatetime:
    @staticmethod
    def now() -> str:
        """获取当前时间"""
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    @staticmethod
    def unique_time_string() -> str:
        """获取当前时间"""
        return datetime.datetime.now().strftime(f"%Y%m%d-%H%M%S-{shortuuid.uuid()[:6]}")

    @staticmethod
    def to_string(datetime: datetime.datetime, fmt: str = "%Y-%m-%d %H:%M:%S") -> str:
        """将 datetime 对象转换为字符串"""
        return datetime.strftime(fmt)

    @staticmethod
    def to_string_tz(datetime: datetime.datetime) -> str:
        """将 datetime 对象转换为带时区的字符串"""
        datetime = pytz.UTC.localize(datetime).isoformat()
        return datetime

    @staticmethod
    def set_midnight(dt: datetime):
        return datetime.datetime.combine(dt.date(), datetime.time())
