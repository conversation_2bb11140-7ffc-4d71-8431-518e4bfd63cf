import requests
import json
from sutils.log import Slog


class Shttp:
    headers = {"Content-Type": "application/json"}
    auth_data = {}

    def __init__(self, base_url) -> None:
        self.base_url = base_url

    def login(self, endpoint: str, login_data: dict):
        """
        登录并返回 data 的工具函数
        Parameters:
        - login_url (str): 登录请求地址
        - login_data (dict): 包含用户名和密码的字典

        Returns:
        - data (str): 登录成功后获取的 Token，若登录失败则返回空字符串
        """

        # 将数据转换为 JSON 格式
        json_data = json.dumps(login_data)

        # 设置请求头，表明发送的是 JSON 数据
        headers = {"Content-Type": "application/json"}

        # 创建一个会话对象，模拟登录过程
        session = requests.Session()

        # 发送登录请求，使用POST请求方式，并将数据以JSON格式发送
        response = session.post(f"{self.base_url}{endpoint}", data=json_data, headers=headers)
        # 检查登录是否成功
        if response.ok:
            # 解析响应中的 Token 字段
            login_response = response.json()
            Slog.debug(login_response)
            data = login_response.get("data")
            token = data.get('auth').get("access_token")
            self.auth_data = data
            self.headers["Authorization"] = f"Bearer {token}"
            return self
        else:
            return self

    def get(self, url: str, params: dict = None):
        session = requests.Session()
        response = session.get(f"{self.base_url}{url}", headers=self.headers, params=params)

        if response.ok:
            return response
        else:
            Slog.debug("get error")
            Slog.debug(response.text)
            return ""

    def post(self, url: str, data: dict):
        """
        登录并返回 data 的工具函数

        Parameters:
        - url (str): 请求地址
        - data (dict): 包含用户名和密码的字典
        - token (str): 登录成功后获取的 Token

        Returns:
        - data (str): 登录成功后获取的 Token，若登录失败则返回空字符串
        """
        # convert data to json
        json_data = json.dumps(data)
        # create a session object to simulate the login process
        session = requests.Session()
        # 发送POST请求，并将数据以JSON格式发送
        response = session.post(url, data=json_data, headers=self.headers)

        # 检查登录是否成功
        if response.ok:
            # 解析响应中的 Token 字段
            login_response = response.json()
            data = login_response.get("data")
            if data:
                return data
            else:
                return ""
        else:
            return ""

    def put(self, url: str, data: dict):
        try:
            # convert data to json
            json_data = json.dumps(data)
            # create a session object to simulate the login process
            session = requests.Session()
            # 发送POST请求，并将数据以JSON格式发送
            response = session.put(url=f"{self.base_url}{url}", data=json_data, headers=self.headers)

            return response
            # 检查登录是否成功
            # if response.ok:
            #     # 解析响应中的 Token 字段
            #     login_response = response.json()
            #     data = login_response.get("data")
            #     if data:
            #         return data
            #     else:
            #         return ""
            # else:
            #     return ""
        except Exception as e:
            Slog.debug(e)
