import asyncio
from functools import wraps
import inspect
from typing import Callable, Any, TypeVar

T = TypeVar("T")


def to_async(func: Callable[..., T]) -> Callable[..., asyncio.Future[T]]:
    """
    将同步函数转换为异步函数的装饰器

    Args:
        func: 要转换的同步函数

    Returns:
        转换后的异步函数

    Example:
        @to_async
        def slow_operation(x: int) -> int:
            time.sleep(1)  # 模拟耗时操作
            return x * 2

        async def main():
            result = await slow_operation(5)
            print(result)  # 输出: 10
    """

    @wraps(func)
    async def wrapper(*args: Any, **kwargs: Any) -> T:
        # 检查函数是否已经是协程
        if inspect.iscoroutinefunction(func):
            return await func(*args, **kwargs)

        # 使用线程池执行同步函数
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, lambda: func(*args, **kwargs))

    return wrapper
