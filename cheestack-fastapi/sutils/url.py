from urllib.parse import urlparse


class Surl:
    def __init__(self) -> None:
        pass

    @staticmethod
    def extract_path(url):
        """
        @param url: 网址
        @return: 去除协议和域名后的路径
        """
        # 使用 urlparse 解析网址
        parsed_url = urlparse(url)
        # 检查网址是否包含协议部分
        if parsed_url.scheme:
            # 如果包含协议部分，则认为是完整的网址，提取路径并去掉域名部分
            path = parsed_url.path
        else:
            # 如果不包含协议部分，则认为是路径，不需要进行处理
            path = url

        return path

    @staticmethod
    def add_base_url(cls, data, base_url):
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, dict) or isinstance(value, list):
                    cls.add_base_url(value)
                elif isinstance(value, str) and key.lower().endswith("url"):
                    data[key] = f"{base_url}{value}"
        elif isinstance(data, list):
            for item in data:
                cls.add_base_url(item)
        return data


if __name__ == "__main__":
    url = "https://www.baidu.com/test_path?wd=python"
    print(Surl.extract_path(url))
