from calendar import c
from paddleocr import PaddleOCR
import fitz  # pip install PyMuPDF
import os
from PIL import Image
import numpy as np
from sutils.log import Slog
from sutils.string import Sstring


class Socr:
    # Paddleocr目前支持的多语言语种可以通过修改lang参数进行切换
    # 例如`ch`, `en`, `fr`, `german`, `korean`, `japan`

    def img_to_text(self, img_path: str, lang="en"):

        # 读取图像
        # img = Image.open(
        #     "/Users/<USER>/work/project/cheestack/cheestack-scripts/assets/raz/cropped_raz_imgs/C/Who__Who__Who_Im6.jpg"
        # )
        img = Image.open(img_path)
        # 获取图像尺寸
        width, height = img.size
        font_height = None
        if width > height:
            font_height = 300
        else:
            font_height = 400

        # 计算底部100像素的区域(raz文字部分)
        bottom_region = (0, (height - font_height), width, height)
        # 裁剪图像
        cropped_img = img.crop(bottom_region)
        # cropped_img.show()

        ocr = PaddleOCR(use_angle_cls=True, use_gpu=False, lang=lang)
        result = ocr.ocr(np.array(cropped_img), cls=True)

        Slog.debug(result)

        try:
            string_list = []
            for i in range(len(result[0])):
                Slog.debug(result[0][i][1][0])
                Slog.debug(result[0][i][1][1])
                # 如果置信度大于0.9
                if result[0][i][1][1] > 0.85:
                    # 添加文字到列表
                    string_list.append(result[0][i][1][0])
                else:
                    Slog.debug("置信度小于0.85")
                    continue
            return Sstring.list_word_to_sentence(string_list)
        except Exception as e:
            print(e)
            return None

    def pdf_to_jpg(self, name, language):
        ocr = PaddleOCR(
            use_angle_cls=True, use_gpu=False, lang=language
        )  # need to run only once to download and load model into memory
        pdfdoc = fitz.open(name)
        for pg in range(pdfdoc.page_count):
            page = pdfdoc[pg]
            rotate = int(0)
            # 每个尺寸的缩放系数为2，这将为我们生成分辨率提高四倍的图像。
            zoom_x = 2.0
            zoom_y = 2.0
            trans = fitz.Matrix(zoom_x, zoom_y).prerotate(rotate)
            pm = page.get_pixmap(matrix=trans, alpha=False)
            pm._writeIMG("temp.jpg", 1)

            # ocr识别
            result = ocr.ocr("temp.jpg", cls=True)

            # 提取文件名
            xx = os.path.splitext(name)
            filename = xx[0].split("\\")[-1] + ".txt"
            # 存储结果
            with open(filename, mode="a") as f:
                for line in result:
                    # 置信度大于0.9的才保留
                    if line[1][1] > 0.9:
                        print(line[1][0].encode("utf-8").decode("utf-8"))
                        f.write(line[1][0].encode("utf-8").decode("utf-8") + "\n")
            print(pg)


if __name__ == "__main__":
    try:
        language = "en"
        # img_path = "images/At the Lake.pdf_2_1_Im2.jpg"
        img_path = "res/cropped_raz_imgs/A/Where_To_Go_Im6.jpg"
        # img_path = "res/cropped_raz_imgs/A/A_Fish_Sees_Im3.jpg"
        # img_path = "res/cropped_raz_imgs/A/A_Fish_Sees_Im5.jpg"
        result_img = "resutl.jpg"
        rst = Socr.img_to_text(img_path, language)
        print(rst)
    except Exception as e:
        print(e)
