import re


class Svalidator:
    def __init__(self):
        pass

    @staticmethod
    def check_string(pattern: str, string: str, flag: int = 0) -> bool:
        pattern: str = re.compile(pattern, flag)
        if pattern.match(string):
            return True
        else:
            return False


class Spattern:
    def __init__():
        pass

    # 电话号码(可带国家区号)
    mobile = r"^\+?\d{10,15}$"
    # 中国大陆电话号码
    chiness_mobile = r"^((13[0-9])|(14[0-9])|(15[0-9])|(16[0-9])|(17[0-9])|(18[0-9])|(19[0-9]))\d{8}$"
    # 用户名正则：2到16位（字母或汉字打头，字母，数字，汉字，下划线，减号）
    username = r"^([A-Za-z]|[\u4e00-\u9fa5])[A-Za-z0-9\u4e00-\u9fa5-_]{1,29}$"
    # 密码正则：6-20位，至少包含一个英文字母和一个数字， 可以包含特殊字符
    password = r"^[a-zA-Z0-9!@#$%^&*()_+]{6,20}$"


# - 密码由数字和字母组成，长度在 6 到 20 个字符之间： `^[a-zA-Z0-9]{6,20}$`
# - 密码由数字、字母和特殊字符组成，长度在 6 到 20 个字符之间： `^[a-zA-Z0-9~!@#$%^&*()_+` \-={}[\]:;\"'<>,.?\/]{6,20}$`
# - 密码必须至少包含一个大写字母、一个小写字母、一个数字，长度在 8 到 20 个字符之间：` ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,20}$ `
# - 密码必须至少包含一个大写字母、一个小写字母、一个数字和一个特殊字符（如上一个，长度在 8 到 20 个字符之间）：` ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+ `\-={}[\]:;\"'<>,.?\/])[a-zA-Z\d~!@#$%^&*()_+` \-={}[\]:;\"'<>,.?\/]{8,20}$`
#  这些正则表达式可以根据实际情况进行选择和修改。
