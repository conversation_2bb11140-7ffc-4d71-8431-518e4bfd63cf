import json
from tortoise.expressions import Q
from sutils.string import Sstring
from datetime import datetime
from tortoise.queryset import QuerySet


class Squery:
    def __init__(self):
        pass

    @staticmethod
    def dict_to_query(filters: dict) -> Q:
        """
        将字典的过滤条件转换为Q对象
        @filters: 过滤条件的字典表示，例如: {"name__contains": "john", "age__gte": 18}
        @return: 返回Q对象
        @example: or 和 and 可以混合使用。具体来说，如果您想查询 name 字段中包含 "john" 或 "doe"，且 age 大于等于 18，且 gender 为 "male" 或 "female" 的数据，
        可以这样构造 filters 参数：
            filters = {
                "or": [
                    {"name__contains": "john"},
                    {"name__contains": "doe"},
                ],
                "age__gte": 18,
                "or": [
                    {"gender": "male"},
                    {"gender": "female"},
                ],
            }
        """

        query = Q()

        # 尝试将过滤条件解析为 JSON 字符串
        try:
            filters = json.loads(filters)
            for key, value in filters.items():
                if key == "or":
                    or_query = Q()
                    # 如果是 or 查询, 则遍历 value, 递归调用 dict_to_q
                    for sub_filter in value:
                        # 合并 or 查询, 例如: Q(name__contains="john") | Q(name__contains="doe")
                        or_query |= Squery.dict_to_query(sub_filter)
                    # 将 or 查询合并到主查询中
                    query &= or_query
                else:
                    # 尝试转换时间字符串
                    datetime_value = Sstring.utc_time_str_to_datetime(value)
                    if datetime_value:
                        # 如果是时间字符串，则将其转换为 datetime 对象
                        value = datetime_value
                    query &= Q(**{key: value})
            return query

        except json.JSONDecodeError:
            return query

    @staticmethod
    def filter(queryset: QuerySet, filters: dict) -> Q:
        """
        将字典的过滤条件转换为Q对象
        @filters: 过滤条件的字典表示，例如: {"name__contains": "john", "age__gte": 18}
        @return: 返回Q对象
        @example: or 和 and 可以混合使用。具体来说，如果您想查询 name 字段中包含 "john" 或 "doe"，且 age 大于等于 18，且 gender 为 "male" 或 "female" 的数据，
        可以这样构造 filters 参数：
            filters = {
                "or": [
                    {"name__contains": "john"},
                    {"name__contains": "doe"},
                ],
                "age__gte": 18,
                "or": [
                    {"gender": "male"},
                    {"gender": "female"},
                ],
            }
        """
        query = Q()
        # 尝试将过滤条件解析为 JSON 字符串
        if isinstance(filters, str):
            try:
                filters = json.loads(filters)
            except json.JSONDecodeError:
                return query

        for key, value in filters.items():
            if key == "or":
                # 合并 or 查询, 例如: Q(name__contains="john") | Q(name__contains="doe")
                query &= Q.OR(*[Squery.dict_to_query(sub_filter) for sub_filter in value])
            elif key == "and":
                # 合并 and 查询
                query &= Q.AND(*[Squery.dict_to_query(sub_filter) for sub_filter in value])
            else:
                # 尝试转换时间字符串
                try:
                    value = datetime.fromisoformat(value)
                except ValueError:
                    pass
                query &= Q(**{key: value})
        queryset.filter(query)
        return query
