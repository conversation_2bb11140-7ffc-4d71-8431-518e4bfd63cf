from pydantic import BaseModel
from tortoise.models import Model


class Smodel:
    @staticmethod
    def model_to_dict(model: Model, schema: BaseModel):
        """Converts a tortoise model to a dictionary
        @param model: tortoise model
        @param schema: pydantic schema
        @return: dict
        """
        # `BaseModel`的`dict`方法返回的的字典无法正确转换datetime类型, 但`json`方法可以,
        # 所以先转成json字符串, 然后读取转为json
        # data = json.loads(schema.from_orm(model).json())
        # return data
        pydantic_instance = schema.model_validate(model, from_attributes=True)
        data = pydantic_instance.model_dump(mode="json")
        return data

    @staticmethod
    def models_to_dict(models: list[Model], schema: BaseModel):
        """Converts a list of tortoise models to a list of dictionaries
        @param models: list of tortoise models
        @param schema: pydantic schema
        @return: list of dict
        """
        return [Smodel.model_to_dict(model, schema) for model in models]
