import logging  # 导入 logging 模块，用于日志记录  # 导入 TimedRotatingFileHandler 类，用于定时切割日志文件


class BorderedFormatter(logging.Formatter):
    def format(self, record):
        formatted_message = super().format(record)
        return f"------------->\n{formatted_message}\n<-------------"


def init_logger(logger_name=None):
    if logger_name is None:
        logger_name = __name__

    if logger_name not in logging.Logger.manager.loggerDict:

        logger = logging.getLogger(logger_name)
        # 设置 propagate 为 False，防止日志消息被父日志记录器重复记录
        logger.propagate = False
        logger.setLevel(logging.DEBUG)

        log_config = {
            "format": "%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d\n%(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        }

        formatter = BorderedFormatter(log_config["format"], log_config["datefmt"])

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.DEBUG)
        logger.addHandler(console_handler)

    return logging.getLogger(logger_name)


Slog = init_logger("general")  # 初始化名为 "general" 的日志记录器
