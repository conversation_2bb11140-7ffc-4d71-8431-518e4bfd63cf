# list util
class Slist:
    @staticmethod
    def remove_duplicates_dict_by_key(data, key):
        """
        根据指定的键去除嵌套列表中的重复项

        Parameters:
        - data (list): 嵌套字典列表
        - key (str): 用于去重的键名

        Returns:
        - list: 去重后的字典列表
        """
        seen_values = set()
        result = []
        for item in data:
            value = item.get(key)
            if value not in seen_values:
                result.append(item)
                seen_values.add(value)
        return result
