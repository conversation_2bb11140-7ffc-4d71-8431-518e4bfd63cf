import json
import os
import redis


class RedisUtil:
    def __init__(self, master_url: str, client_url: str):
        master_pool = redis.ConnectionPool.from_url(master_url, decode_responses=True)
        client_pool = redis.ConnectionPool.from_url(client_url, decode_responses=True)
        self.master_cache = redis.Redis(connection_pool=master_pool)
        self.client_cache = redis.Redis(connection_pool=client_pool)

    def get(self, name: str):
        try:
            return self.client_cache.get(name)
        except:
            return self.master_cache.get(name)

    def set(self, name: str, value: str, ex: int):
        return self.master_cache.set(name=name, value=value, ex=ex)

    def delete(self, name: str):
        return self.master_cache.delete(name)

    # set json
    def set_json(self, name: str, value: dict, ex: int):
        value = json.dumps(value)
        return self.master_cache.set(name=name, value=value, ex=ex)

    # get json
    def get_json(self, name: str):
        value = self.client_cache.get(name)
        if value is None:
            return {}
        return json.loads(value)

Scache = RedisUtil(
    master_url=os.getenv("REDIS_URL", ""),
    client_url=os.getenv("REDIS_URL_CLIENT", "")
)
