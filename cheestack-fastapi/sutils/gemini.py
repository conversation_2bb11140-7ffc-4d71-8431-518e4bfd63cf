import google.generativeai as genai
from sutils.proxy import Sproxy


class Sgemini:
    @staticmethod
    def translate_raz(text: str):
        """
        使用 Gemini 模型将 RAZ 文本翻译成中文。

        Args:
          text: 要翻译的文本。

        Returns:
          翻译后的文本。
        """

        # 设置代理
        Sproxy.set_proxy()
        genai.configure(api_key="AIzaSyANA5ytj0quFMnDGm4Vi4KA7tTe5YMMDIY", transport="rest")

        # 配置模型
        generation_config = {
            "temperature": 0.9,
            "top_p": 1,
            "top_k": 1,
            "max_output_tokens": 2048,
        }
        safety_settings = [{"enable_safety_filter": False}]

        safety_settings = [
            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"},
        ]
        model = genai.GenerativeModel(
            model_name="gemini-pro", generation_config=generation_config, safety_settings=safety_settings
        )
        # 开始对话
        convo = model.start_chat(
            history=[
                {
                    "role": "user",
                    "parts": '你现在是一名非常专业英文老师, 有10年的raz教学经验, 现在你正在帮助学校将raz的内容翻译成地道的中文, 我会发送一些英文句子, 例如这样的:`what is this?`, 你要帮我翻译成中文, 注意,一定要翻译成非常地道,注意各种语法,请勿返回多余的问题,仅回复翻译后的中文, 第一个句子是"hello, world"',
                },
                {"role": "model", "parts": "你好，世界"},
                {"role": "user", "parts": "The fire"},
                {"role": "model", "parts": "火焰"},
            ]
        )
        # 发送文本并获取翻译结果
        convo.send_message(text)
        Sproxy.unset_proxy()
        return convo.last.parts[0].text


if __name__ == "__main__":
    # 翻译示例
    text = "The yellow vest."
    translation = Sgemini.translate_raz(text)
    print(f"翻译结果：{translation}")
