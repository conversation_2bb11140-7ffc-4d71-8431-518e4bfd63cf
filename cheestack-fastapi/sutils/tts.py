#!/usr/bin/env python3
import asyncio
import edge_tts
import azure.cognitiveservices.speech as speechsdk
import os
from sutils.log import Slog
from sutils.string import Sstring


class EdgeVoice:
    us_AnaNeural = "en-US-AnaNeural"
    us_SteffanNeural = "en-US-SteffanNeural"
    us_RogerNeural = "en-US-RogerNeural"
    us_JennyNeural = "en-US-JennyNeural"
    us_EricNeural = "en-US-EricNeural"
    us_AriaNeural = "en-US-AriaNeural"
    us_AndrewNeural = "en-US-AndrewNeural"
    zh_XiaoxiaoNeural = "zh-CN-XiaoxiaoNeural"
    zh_XiaoyiNeural = "zh-CN-XiaoyiNeural"
    zh_YunjianNeural = "zh-CN-YunjianNeural"
    zh_YunxiNeural = "zh-CN-YunxiNeural"
    zh_YunxiaNeural = "zh-CN-YunxiaNeural"
    zh_YunyangNeural = "zh-CN-YunyangNeural"


class AzureVoice:
    zh_CN_XiaochenMultilingualNeural = "zh-CN-XiaochenMultilingualNeural"
    zh_CN_XiaoxiaoMultilingualNeural = "zh-CN-XiaoxiaoMultilingualNeural"
    zh_CN_XiaoxiaoNeural = "zh-CN-XiaoxiaoNeural"

    zh_CN_YunyiMultilingualNeural = "zh-CN-YunyiMultilingualNeural"
    zh_CN_YunyiMultilingualNeural = "zh-CN-YunyiMultilingualNeural"

    en_US_AdamMultilingualNeural = "en-US-AdamMultilingualNeural"
    en_US_AndrewMultilingualNeural = "en-US-AndrewMultilingualNeural"  # 聊天, 广告
    en_US_DerekMultilingualNeural = "en-US-DerekMultilingualNeural"  # 旁白,在线学习
    en_GB_OllieMultilingualNeural = "en-GB-OllieMultilingualNeural"  # 英式
    # en_US_AnaNeural = "en-US-AnaNeural"

# config.SpeechSynthesisVoiceName = "en-US-BrianMultilingualNeural"


class AzureTTS:
    @staticmethod
    def tts(
        text: str,
        output: str,
        voice: str = AzureVoice.zh_CN_XiaoxiaoMultilingualNeural,
        rate: int = "-20%",
    ):

        # 设置语音合成配置
        speech_config = speechsdk.SpeechConfig(
            subscription=os.environ.get("SPEECH_KEY"), region=os.environ.get("SPEECH_REGION")
        )
        speech_config.set_speech_synthesis_output_format(
            speechsdk.SpeechSynthesisOutputFormat.Audio16Khz32KBitRateMonoMp3
        )
        # use_default_speaker=True 时会直接播放
        audio_config = speechsdk.audio.AudioOutputConfig(filename=output)
        # get the synthesizer
        speech_synthesizer = speechsdk.SpeechSynthesizer(
            speech_config=speech_config, audio_config=audio_config
        )
        # lang = "en-US"
        Slog.debug(voice)
        ssml = f"""
        <speak version="1.0" xmlns="https://www.w3.org/2001/10/synthesis" xml:lang="en-US">
        <voice name="{voice}">
            <prosody rate="{rate}">
                {text}
            </prosody>
        </voice>
        </speak>
        """

        # synthesizing the speech
        speech_synthesis_result = speech_synthesizer.speak_ssml_async(ssml).get()
        # speech_synthesis_result = speech_synthesizer.speak_text_async(text).get()
        # check the result
        if speech_synthesis_result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
            return output
        elif speech_synthesis_result.reason == speechsdk.ResultReason.Canceled:
            cancellation_details = speech_synthesis_result.cancellation_details
            print("Speech synthesis canceled: {}".format(cancellation_details.reason))
            if cancellation_details.reason == speechsdk.CancellationReason.Error:
                if cancellation_details.error_details:
                    print("Error details: {}".format(cancellation_details.error_details))
                    print("Did you set the speech resource key and region values?")
        return None


class Stts:
    @staticmethod
    async def tts_async(
        text: str,
        output: str,
        voice: str = None,
        rate: float = r"-30%",
    ):
        language = Sstring.detect_language(text)
        text = Sstring.remove_punctuation_for_tts(text)
        if not voice:
            if language == "zh":
                if len(text) >= 5:
                    voice = AzureVoice.zh_CN_XiaoxiaoMultilingualNeural
                else:
                    voice = AzureVoice.zh_CN_XiaoxiaoNeural
            elif language == "en":
                voice = AzureVoice.en_US_AndrewMultilingualNeural
        Slog.debug(output)
        # 使用Azure TTS
        resut = AzureTTS.tts(text, output, voice, rate)
        if resut:
            return resut
        # 如果上述方法失败，尝试edge-tts
        if language == "zh":
            voice = EdgeVoice.zh_XiaoxiaoNeural
        elif language == "en":
            voice = EdgeVoice.us_AnaNeural

        rate = "+0%"
        communicate = edge_tts.Communicate(text, voice, rate=rate, proxy="http://100.107.140.151:7890")
        if communicate:
            await communicate.save(output)
        return None

    @staticmethod
    def tts(text: str, output: str, voice: str = EdgeVoice.us_AnaNeural, rate: str = "+0%"):
        async def amain():
            await Stts._try_tts_with_retry(text, output, voice, rate=rate)

        loop = asyncio.get_event_loop_policy().get_event_loop()  # 获取 asyncio 事件循环
        try:
            loop.run_until_complete(amain())  # 运行异步任务，直到任务完成
        except Exception as e:
            print(e)