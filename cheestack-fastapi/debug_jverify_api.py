#!/usr/bin/env python3
"""
极光认证API独立测试脚本
用于排查token invalid问题
"""

import requests
import base64
import json
from datetime import datetime

# 配置信息
APP_KEY = "b24858b6ec7a90b077dbb8a1"
MASTER_SECRET = "6936c0a34a3961723a8dd3e0"
API_BASE = "https://api.verification.jpush.cn"

# 测试用的Token（从最新日志中获取）
TEST_TOKEN = "iHa_efLOjNCikVW492fWSkBl2sU-4rdoPReXbsEk9xMXhlhA7mBwUQA_c5B0-mD7AC6jNP045FjkyGdF6zbuDhJ69Mgq0AE0TL-QgC4UTUC4BpQfIEhzBglbdPDkqSK38fo4UYvCCTJ0Na99_Rh0ql1kh3h1KSbnAxwtu8C3sj4WOonfEKGlDR5mgZKumBjkd6lxP0HgJ42UgeJeM6amrI9Ab5igukio1xFkGT5Kyo1on0K_saRgyoSKfqad2MbtDpnmqx1Yjuywe3LirEcB7w=="


def get_auth_header():
    """获取认证头"""
    auth_string = f"{APP_KEY}:{MASTER_SECRET}"
    auth_bytes = auth_string.encode("utf-8")
    auth_b64 = base64.b64encode(auth_bytes).decode("utf-8")
    return f"Basic {auth_b64}"


def test_api_call(token, endpoint="/v1/web/loginTokenVerify"):
    """测试API调用"""
    url = f"{API_BASE}{endpoint}"

    headers = {
        "Content-Type": "application/json",
        "Authorization": get_auth_header(),
        "Accept": "application/json",
        "User-Agent": "JVerify-Debug/1.0",
    }

    data = {"loginToken": token}

    print("=" * 80)
    print("🧪 极光认证API独立测试")
    print(f"⏰ 测试时间: {datetime.now()}")
    print(f"📍 请求URL: {url}")
    print(f"🔑 AppKey: {APP_KEY}")
    print(f"🔑 MasterSecret: {MASTER_SECRET[:10]}...")
    print(f"📱 Token长度: {len(token)}")
    print(f"📱 Token前50字符: {token[:50]}...")
    print(f"📤 请求头: {json.dumps(headers, indent=2)}")
    print(f"📦 请求数据: {json.dumps(data, indent=2)}")

    try:
        print("\n⏳ 发送HTTP请求...")
        response = requests.post(url, headers=headers, json=data, timeout=10)

        print(f"📥 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")

        try:
            response_json = response.json()
            print(f"📃 响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)}")

            if response_json.get("code") == 8000:
                print("✅ API调用成功!")
                return response_json
            else:
                print(f"❌ API调用失败: {response_json}")
                return None

        except json.JSONDecodeError:
            print(f"❌ 响应不是有效的JSON: {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求异常: {e}")
        return None


def test_different_endpoints():
    """测试不同的API端点"""
    endpoints = [
        "/v1/web/loginTokenVerify",  # 移动端
        "/v1/web/h5/loginTokenVerify",  # Web端
    ]

    for endpoint in endpoints:
        print(f"\n🎯 测试端点: {endpoint}")
        result = test_api_call(TEST_TOKEN, endpoint)
        if result:
            print("✅ 找到可用端点!")
            return endpoint, result

    return None, None


def test_auth_header():
    """测试认证头格式"""
    auth_header = get_auth_header()
    print(f"\n🔑 认证头测试:")
    print(f"   - 原始字符串: {APP_KEY}:{MASTER_SECRET}")
    print(f"   - Base64编码: {auth_header}")

    # 验证Base64解码
    try:
        decoded = base64.b64decode(auth_header.replace("Basic ", "")).decode("utf-8")
        print(f"   - 解码验证: {decoded}")
        if decoded == f"{APP_KEY}:{MASTER_SECRET}":
            print("   ✅ 认证头格式正确")
        else:
            print("   ❌ 认证头格式错误")
    except Exception as e:
        print(f"   ❌ 认证头解码失败: {e}")


def test_with_fresh_token():
    """等待用户输入新Token进行测试"""
    print("\n" + "=" * 80)
    print("🔄 实时Token测试")
    print("📋 请按以下步骤操作:")
    print("   1. 在Flutter应用中获取最新Token")
    print("   2. 复制Token内容")
    print("   3. 粘贴到下面的输入框")
    print("   4. 立即按回车进行验证")
    print("=" * 80)

    try:
        fresh_token = input("\n📱 请输入最新获取的Token: ").strip()

        if not fresh_token:
            print("❌ Token为空，测试取消")
            return

        if len(fresh_token) < 100:
            print(f"⚠️ Token长度({len(fresh_token)})似乎太短，确认是完整Token吗？")
            confirm = input("是否继续测试？(y/n): ").strip().lower()
            if confirm != "y":
                return

        print(f"\n🚀 开始测试新Token (长度: {len(fresh_token)})")

        # 立即测试
        endpoint, result = test_different_endpoints_with_token(fresh_token)

        if result:
            print(f"\n🎉 测试成功! 可用端点: {endpoint}")
            print("✅ 问题解决：Token验证成功!")
        else:
            print(f"\n💥 新Token测试仍然失败!")
            print("🔍 这说明问题不是Token时效性，而是其他配置问题")

    except KeyboardInterrupt:
        print("\n\n❌ 用户取消测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")


def test_different_endpoints_with_token(token):
    """使用指定Token测试不同的API端点"""
    endpoints = [
        "/v1/web/loginTokenVerify",  # 移动端
        "/v1/web/h5/loginTokenVerify",  # Web端
    ]

    for endpoint in endpoints:
        print(f"\n🎯 测试端点: {endpoint}")
        result = test_api_call(token, endpoint)
        if result:
            print("✅ 找到可用端点!")
            return endpoint, result

    return None, None


def check_jiguang_config():
    """检查极光控制台配置"""
    print("\n" + "=" * 80)
    print("🔧 极光控制台配置检查")
    print("=" * 80)

    print("📋 请检查极光控制台以下配置:")
    print("\n1. 🏢 应用信息:")
    print(f"   - AppKey: {APP_KEY}")
    print(f"   - MasterSecret: {MASTER_SECRET}")
    print("   ✅ 确认这些信息与极光控制台完全一致")

    print("\n2. 📱 应用平台配置:")
    print("   - Android包名: 是否正确配置")
    print("   - iOS Bundle ID: 是否正确配置")
    print("   - 证书配置: 是否上传正确")

    print("\n3. 🔐 认证服务配置:")
    print("   - JVerify服务: 是否已开启")
    print("   - 运营商配置: 是否支持当前运营商")
    print("   - RSA密钥对: 是否已生成并配置")

    print("\n4. 🌐 网络环境:")
    print("   - 服务器IP白名单: 是否已添加")
    print("   - 域名白名单: 是否已配置")

    print("\n5. 💰 服务状态:")
    print("   - 账户余额: 是否充足")
    print("   - 服务状态: 是否正常")
    print("   - 调用限制: 是否超出限制")

    print("\n💡 常见问题排查:")
    print("   1. AppKey不匹配 → 检查Flutter和后端配置")
    print("   2. 服务未开启 → 在控制台开启JVerify服务")
    print("   3. 运营商不支持 → 检查当前运营商是否在支持列表")
    print("   4. 网络环境限制 → 确保在蜂窝网络下测试")
    print("   5. 账户问题 → 检查余额和服务状态")


def main():
    """主函数"""
    print("🚀 开始极光认证API独立测试")

    # 1. 测试认证头
    test_auth_header()

    # 2. 检查极光配置
    check_jiguang_config()

    # 3. 测试旧Token（预期失败）
    print("\n📋 测试旧Token（预期会失败）:")
    endpoint, result = test_different_endpoints()

    if result:
        print(f"\n🎉 意外成功! 可用端点: {endpoint}")
    else:
        print("\n💥 旧Token测试失败（符合预期）")
        print("\n🔍 失败原因分析:")
        print("   ✅ 认证头格式正确")
        print("   ✅ API端点可达")
        print("   ❌ Token无效（可能已过期或被使用）")

        # 4. 实时Token测试
        test_with_fresh_token()


if __name__ == "__main__":
    main()
