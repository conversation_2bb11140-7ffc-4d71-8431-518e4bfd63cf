from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "access" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "access_name" VARCHAR(15) NOT NULL,
    "parent_id" INT NOT NULL  DEFAULT 0,
    "scopes" VARCHAR(255) NOT NULL UNIQUE,
    "access_desc" VARCHAR(255),
    "menu_icon" VARCHAR(255),
    "is_check" BOOL NOT NULL  DEFAULT False,
    "is_menu" BOOL NOT NULL  DEFAULT False
);
COMMENT ON COLUMN "access"."created_at" IS '创建时间';
COMMENT ON COLUMN "access"."updated_at" IS '更新时间';
COMMENT ON COLUMN "access"."access_name" IS '权限名称';
COMMENT ON COLUMN "access"."parent_id" IS '父id';
COMMENT ON COLUMN "access"."scopes" IS '权限范围标识';
COMMENT ON COLUMN "access"."access_desc" IS '权限描述';
COMMENT ON COLUMN "access"."menu_icon" IS '菜单图标';
COMMENT ON COLUMN "access"."is_check" IS '是否验证权限 True为验证 False不验证';
COMMENT ON COLUMN "access"."is_menu" IS '是否为菜单 True菜单 False不是菜单';
COMMENT ON TABLE "access" IS '权限表';
CREATE TABLE IF NOT EXISTS "access_log" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "target_url" VARCHAR(255),
    "user_agent" VARCHAR(255),
    "request_params" JSONB,
    "ip" VARCHAR(32),
    "note" VARCHAR(255)
);
COMMENT ON COLUMN "access_log"."created_at" IS '创建时间';
COMMENT ON COLUMN "access_log"."updated_at" IS '更新时间';
COMMENT ON COLUMN "access_log"."target_url" IS '访问的url';
COMMENT ON COLUMN "access_log"."user_agent" IS '访问UA';
COMMENT ON COLUMN "access_log"."request_params" IS '请求参数get|post';
COMMENT ON COLUMN "access_log"."ip" IS '访问IP';
COMMENT ON COLUMN "access_log"."note" IS '备注';
COMMENT ON TABLE "access_log" IS '用户操作记录表';
CREATE TABLE IF NOT EXISTS "role" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "role_name" VARCHAR(15) NOT NULL,
    "role_status" BOOL NOT NULL,
    "role_desc" VARCHAR(255)
);
COMMENT ON COLUMN "role"."created_at" IS '创建时间';
COMMENT ON COLUMN "role"."updated_at" IS '更新时间';
COMMENT ON COLUMN "role"."role_name" IS '角色名称';
COMMENT ON COLUMN "role"."role_status" IS 'True:启用 False:禁用';
COMMENT ON COLUMN "role"."role_desc" IS '角色描述';
COMMENT ON TABLE "role" IS '角色表';
CREATE TABLE IF NOT EXISTS "system_params" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "params_name" VARCHAR(255) NOT NULL UNIQUE,
    "params" JSONB NOT NULL
);
COMMENT ON COLUMN "system_params"."created_at" IS '创建时间';
COMMENT ON COLUMN "system_params"."updated_at" IS '更新时间';
COMMENT ON COLUMN "system_params"."params_name" IS '参数名';
COMMENT ON COLUMN "system_params"."params" IS '参数';
COMMENT ON TABLE "system_params" IS '系统参数表';
CREATE TABLE IF NOT EXISTS "user" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" VARCHAR(100) NOT NULL  PRIMARY KEY,
    "password" VARCHAR(100) NOT NULL,
    "username" VARCHAR(30)  UNIQUE,
    "mobile" VARCHAR(100)  UNIQUE,
    "email" VARCHAR(100),
    "avatar" VARCHAR(200),
    "intro" TEXT,
    "is_superuser" BOOL NOT NULL  DEFAULT False,
    "status" INT NOT NULL  DEFAULT 1
);
COMMENT ON COLUMN "user"."created_at" IS '创建时间';
COMMENT ON COLUMN "user"."updated_at" IS '更新时间';
COMMENT ON COLUMN "user"."id" IS 'shortuuid';
COMMENT ON COLUMN "user"."password" IS '用户密码';
COMMENT ON COLUMN "user"."username" IS '用户名';
COMMENT ON COLUMN "user"."mobile" IS '手机号码';
COMMENT ON COLUMN "user"."email" IS '用户邮箱';
COMMENT ON COLUMN "user"."avatar" IS '用户头像';
COMMENT ON COLUMN "user"."intro" IS '个人简介';
COMMENT ON COLUMN "user"."is_superuser" IS '用户类型 True:超级管理员 False:普通管理员';
COMMENT ON COLUMN "user"."status" IS '1正常,2禁用,3已删除,';
COMMENT ON TABLE "user" IS '用户表';
CREATE TABLE IF NOT EXISTS "asset" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "type" VARCHAR(5) NOT NULL  DEFAULT 'file',
    "name" VARCHAR(255) NOT NULL,
    "url" VARCHAR(255) NOT NULL,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "asset"."created_at" IS '创建时间';
COMMENT ON COLUMN "asset"."updated_at" IS '更新时间';
COMMENT ON COLUMN "asset"."type" IS '类型';
COMMENT ON COLUMN "asset"."name" IS '名称';
COMMENT ON COLUMN "asset"."url" IS '地址';
CREATE TABLE IF NOT EXISTS "book" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "name" VARCHAR(64),
    "brief" TEXT,
    "cover" VARCHAR(256),
    "privacy" VARCHAR(11) NOT NULL,
    "user_id" VARCHAR(100) REFERENCES "user" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "book"."created_at" IS '创建时间';
COMMENT ON COLUMN "book"."updated_at" IS '更新时间';
COMMENT ON COLUMN "book"."name" IS '书名';
COMMENT ON COLUMN "book"."brief" IS '简介';
COMMENT ON COLUMN "book"."cover" IS '封面';
COMMENT ON COLUMN "book"."privacy" IS '类型';
CREATE TABLE IF NOT EXISTS "config" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "is_auto_play_audio" BOOL   DEFAULT True,
    "is_auto_play_ai_audio" BOOL   DEFAULT True,
    "review_number" INT   DEFAULT 30,
    "study_number" INT   DEFAULT 10,
    "study_type" INT NOT NULL  DEFAULT 0,
    "current_study_id" INT REFERENCES "book" ("id") ON DELETE SET NULL,
    "editing_book_id" INT REFERENCES "book" ("id") ON DELETE SET NULL,
    "user_id" VARCHAR(100) NOT NULL UNIQUE REFERENCES "user" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "config"."created_at" IS '创建时间';
COMMENT ON COLUMN "config"."updated_at" IS '更新时间';
COMMENT ON COLUMN "config"."is_auto_play_audio" IS '是否自动播放音频';
COMMENT ON COLUMN "config"."is_auto_play_ai_audio" IS '是否自动播放ai音频';
COMMENT ON COLUMN "config"."study_type" IS '当复习结果错误时, 0-维持当前stage,1-进入下一个stage,2-后退一个stage';
COMMENT ON TABLE "config" IS '用户配置';
CREATE TABLE IF NOT EXISTS "book_schedule" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "study_qty" INT NOT NULL  DEFAULT 10,
    "study_first" BOOL NOT NULL  DEFAULT True,
    "active" BOOL NOT NULL  DEFAULT True,
    "book_id" INT NOT NULL REFERENCES "book" ("id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_book_schedu_user_id_700b0d" UNIQUE ("user_id", "book_id")
);
CREATE TABLE IF NOT EXISTS "card" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "type" VARCHAR(18) NOT NULL  DEFAULT 'general',
    "type_version" INT NOT NULL  DEFAULT 1,
    "title" VARCHAR(128),
    "question" TEXT,
    "answer" TEXT,
    "extra" JSONB,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "card"."created_at" IS '创建时间';
COMMENT ON COLUMN "card"."updated_at" IS '更新时间';
COMMENT ON COLUMN "card"."type" IS '类型';
COMMENT ON COLUMN "card"."type_version" IS '版本号';
COMMENT ON COLUMN "card"."title" IS '卡片描述';
COMMENT ON COLUMN "card"."question" IS '问题';
COMMENT ON COLUMN "card"."answer" IS '答案';
COMMENT ON COLUMN "card"."extra" IS '额外信息';
COMMENT ON TABLE "card" IS '知识卡片';
CREATE TABLE IF NOT EXISTS "book_card" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "order" INT NOT NULL  DEFAULT 0,
    "book_id" INT NOT NULL REFERENCES "book" ("id") ON DELETE CASCADE,
    "card_id" INT NOT NULL REFERENCES "card" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "book_card"."order" IS '卡片在书中的顺序';
COMMENT ON TABLE "book_card" IS '_summary_';
CREATE TABLE IF NOT EXISTS "card_asset" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "type" VARCHAR(15) NOT NULL,
    "text" VARCHAR(255),
    "is_correct" BOOL NOT NULL  DEFAULT True,
    "asset_id" INT NOT NULL REFERENCES "asset" ("id") ON DELETE CASCADE,
    "card_id" INT NOT NULL REFERENCES "card" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "card_asset"."type" IS '类型';
COMMENT ON COLUMN "card_asset"."text" IS '文本';
COMMENT ON COLUMN "card_asset"."is_correct" IS '是否正确';
COMMENT ON TABLE "card_asset" IS '卡片与资产中间表';
CREATE TABLE IF NOT EXISTS "card_like" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "card_id" INT NOT NULL REFERENCES "card" ("id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE
);
COMMENT ON TABLE "card_like" IS '卡片与收藏中间表';
CREATE TABLE IF NOT EXISTS "record" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "is_correct" BOOL NOT NULL  DEFAULT True,
    "file" VARCHAR(255),
    "kickoff" BOOL NOT NULL  DEFAULT False,
    "card_id" INT NOT NULL REFERENCES "card" ("id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "record"."created_at" IS '创建时间';
COMMENT ON COLUMN "record"."file" IS '地址';
COMMENT ON COLUMN "record"."kickoff" IS '首次学习';
COMMENT ON TABLE "record" IS 'Study record';
CREATE TABLE IF NOT EXISTS "schedule" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "step" INT NOT NULL  DEFAULT 1,
    "status" VARCHAR(13) NOT NULL  DEFAULT 'learning',
    "next" TIMESTAMPTZ,
    "card_id" INT NOT NULL REFERENCES "card" ("id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_schedule_user_id_eacbca" UNIQUE ("user_id", "card_id")
);
COMMENT ON COLUMN "schedule"."created_at" IS '创建时间';
COMMENT ON COLUMN "schedule"."updated_at" IS '更新时间';
COMMENT ON COLUMN "schedule"."status" IS 'LEARNING: learning\nFINISHED: finished\nSKIP_FINISHED: skip_finished\nPENDING: pending';
COMMENT ON TABLE "schedule" IS 'Study Schedule';
CREATE TABLE IF NOT EXISTS "feedback" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "type" VARCHAR(17) NOT NULL  DEFAULT 'suggestion',
    "content" TEXT,
    "status" VARCHAR(10) NOT NULL  DEFAULT 'submitted',
    "book_id" INT REFERENCES "book" ("id") ON DELETE CASCADE,
    "card_id" INT REFERENCES "card" ("id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "feedback"."created_at" IS '创建时间';
COMMENT ON COLUMN "feedback"."updated_at" IS '更新时间';
COMMENT ON COLUMN "feedback"."type" IS 'VIOLATION: violation\nSUGGESTION: suggestion\nINCORRECT_CONTENT: incorrect_content\nOTHER: other';
COMMENT ON COLUMN "feedback"."status" IS 'SUBMITTED: submitted\nIN_PROCESS: in_process\nRESOLVED: resolved\nCLOSED: closed';
CREATE TABLE IF NOT EXISTS "feedback_comment" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "content" TEXT,
    "feedback_id" INT NOT NULL REFERENCES "feedback" ("id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "feedback_comment"."created_at" IS '创建时间';
COMMENT ON COLUMN "feedback_comment"."updated_at" IS '更新时间';
CREATE TABLE IF NOT EXISTS "release" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "min_version" VARCHAR(255) NOT NULL,
    "build" INT NOT NULL,
    "min_build" INT NOT NULL,
    "apk_url" VARCHAR(255),
    "appstore_id" VARCHAR(255) NOT NULL,
    "changelog" TEXT,
    "release_time" TIMESTAMPTZ
);
CREATE TABLE IF NOT EXISTS "pronunciation" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "name" VARCHAR(128) NOT NULL,
    "url" VARCHAR(128) NOT NULL
);
CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);
CREATE TABLE IF NOT EXISTS "role_access" (
    "role_id" INT NOT NULL REFERENCES "role" ("id") ON DELETE CASCADE,
    "access_id" INT NOT NULL REFERENCES "access" ("id") ON DELETE CASCADE
);
CREATE UNIQUE INDEX IF NOT EXISTS "uidx_role_access_role_id_279853" ON "role_access" ("role_id", "access_id");
CREATE TABLE IF NOT EXISTS "user_role" (
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE,
    "role_id" INT NOT NULL REFERENCES "role" ("id") ON DELETE CASCADE
);
CREATE UNIQUE INDEX IF NOT EXISTS "uidx_user_role_user_id_d0bad3" ON "user_role" ("user_id", "role_id");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
