from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "device" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" VARCHAR(100) NOT NULL  PRIMARY KEY,
    "device_name" VARCHAR(100),
    "device_type" VARCHAR(50),
    "device_model" VARCHAR(100),
    "os_version" VARCHAR(50),
    "app_version" VARCHAR(50),
    "device_token" VARCHAR(255),
    "user_agent" VARCHAR(500),
    "ip_address" VARCHAR(45),
    "last_active" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "is_active" BOOL NOT NULL  DEFAULT True,
    "login_count" INT NOT NULL  DEFAULT 1,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "device"."created_at" IS '创建时间';
COMMENT ON COLUMN "device"."updated_at" IS '更新时间';
COMMENT ON COLUMN "device"."id" IS '设备ID';
COMMENT ON COLUMN "device"."device_name" IS '设备名称';
COMMENT ON COLUMN "device"."device_type" IS '设备类型 (iOS/Android/Web)';
COMMENT ON COLUMN "device"."device_model" IS '设备型号';
COMMENT ON COLUMN "device"."os_version" IS '操作系统版本';
COMMENT ON COLUMN "device"."app_version" IS '应用版本';
COMMENT ON COLUMN "device"."device_token" IS '设备推送Token';
COMMENT ON COLUMN "device"."user_agent" IS '用户代理';
COMMENT ON COLUMN "device"."ip_address" IS 'IP地址';
COMMENT ON COLUMN "device"."last_active" IS '最后活跃时间';
COMMENT ON COLUMN "device"."is_active" IS '是否活跃';
COMMENT ON COLUMN "device"."login_count" IS '登录次数';
COMMENT ON TABLE "device" IS '用户设备表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "device";"""
