from urllib.parse import urlparse
from tortoise import fields
from apps.tcyun.config import tc_settings


class UrlCdnField(fields.CharField):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 在此定义CDN地址
        self.CdnUrl = tc_settings.COS_BASE_URL

    # 从数据库中检索值时执行类似的操作
    def to_python_value(self, value: str) -> str:
        if value:
            # 返回包含完整url的地址
            url = urlparse(value)
            # 如果是完整的url, 则直接返回
            if url.scheme:
                return value
            # 否则拼接CDN地址
            else:
                return f"{self.CdnUrl}{value}"
        else:
            return value

    # 将值存储到数据库时执行的操作
    def to_db_value(self, value: str, instance) -> str:
        if value:
            # 只保存相对地址
            path: str = urlparse(value).path
            # if self.CdnUrl in value:
            #     path = value.replace(self.CdnUrl, "")
            return path
        else:
            return value
