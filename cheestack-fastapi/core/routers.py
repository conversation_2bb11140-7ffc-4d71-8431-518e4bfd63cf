import os
from typing import Type
from fastapi import APIRouter
from tortoise import Model

from apps.auth import apis as user_api
from apps.study import apis as card_api
from apps.study import apis as study_api
from apps.general import apis as general_apis
from apps.pronunciation import apis as pronunciation_api
from apps.tcyun import apis as tcyun_api


# 主从数据库的路由配置
class Router:
    def db_for_read(self, model: Type[Model]):
        return "slave"

    def db_for_write(self, model: Type[Model]):
        return "master"


# 整个app的路由
AppRoutes = APIRouter(prefix="/api")
# 用户管理与授权方面的路由
AppRoutes.include_router(user_api.router, tags=["Authentication"])
AppRoutes.include_router(card_api.router, tags=["app card apis"])
AppRoutes.include_router(pronunciation_api.router, tags=["app pronunciation apis"])
# 学习app的路由
AppRoutes.include_router(study_api.router, tags=["卡片学习"])
AppRoutes.include_router(general_apis.router, tags=["通用信息"])
AppRoutes.include_router(tcyun_api.router, tags=["腾讯云服务"])


# if os.getenv("DEBUG")=="True":
#     from apps.scripts import apis as scripts_apis
#     AppRoutes.include_router(scripts_apis.router, tags=["脚本-DEBUG"])
