# -*- coding:utf-8 -*-
from core.http import HttpCode


# 通用的返回格式
class ApiResponse:
    # 成功返回格式
    @staticmethod
    def success(data: str | dict = [], code=HttpCode.SUCCESS, msg="success"):
        """成功返回格式
        @param code: int, default=1
        @param msg: str, default="success"
        @param data: dict, default=[]
        @return: dict
        """
        return {"code": code, "msg": msg, "data": data}

    @staticmethod
    def fail(data: str | dict = [], code=HttpCode.FAIL, msg="fail"):
        """成功返回格式
        @param code: int, default=1
        @param msg: str, default="success"
        @param data: dict, default=[]
        @return: dict
        """
        return {"code": code, "msg": msg, "data": data}
