# import os
# from dotenv import find_dotenv, load_dotenv
# from fastapi.templating import Jinja2Templates


# DEBUG = True

# # 判断是否为开发环境
# env: str | None = os.getenv("APP_DEBUG")
# if env == "False":
#     # 设置为量产模式
#     DEBUG = False
#     load_dotenv(find_dotenv(), override=True)
#     load_dotenv(find_dotenv(filename=".prodenv"), override=True)
# else:
#     print("开发环境")
#     # 加载环境变量
#     load_dotenv(find_dotenv(), override=True)
#     # load_dotenv(find_dotenv(filename=".testenv"), override=True)
#     load_dotenv(find_dotenv(filename=".prodenv"), override=True)
#     DEBUG = True


# class Configs:
#     NAME: str = "Cheestack"
#     DEBUG: bool = DEBUG
#     VERSION: str = "1.0.0"
#     # (os.path.abspath(__file__)) 返回当前文件的绝对路径
#     # os.path.dirname() 返回当前文件的父目录(不包含文件名)
#     WORKDIR: str = os.path.dirname(os.path.dirname((os.path.abspath(__file__))))

#     # 服务器调试启动参数
#     SERVER_HOST: str = "0.0.0.0"
#     SERVER_URL: str = "http://localhost"
#     SERVER_PORT: int = 8000

#     # 静态资源目录
#     STATIC_DIR: str = os.path.join(os.getcwd(), "static")
#     TEMPLATE_DIR: str = os.path.join(STATIC_DIR, "templates")

#     # 渲染模板templates
#     TEMPLATES = Jinja2Templates(directory=TEMPLATE_DIR)

#     # session配置
#     SECRET_KEY = "session"
#     SESSION_COOKIE = "session_id"
#     # 过期时间, 单位为秒
#     SESSION_MAX_AGE = 14 * 24 * 60 * 60

#     # tocken
#     TOKEN_URL = "/api/v1/auth/login"

#     # JWT
#     JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 365 * 24 * 60
#     JWT_SECRET_KEY = "09d25e094baa6ca2556c818166b7a9563b83f7099f6f0f4caa6cf63b88e8d3e7"
#     JWT_ALGORITHM = "HS256"

#     # 腾讯云对象存储信息
#     COS_SECRET_ID = os.getenv("COS_SECRET_ID")
#     COS_SECRET_KEY = os.getenv("COS_SECRET_KEY")
#     COS_BASE_URL = os.getenv("COS_BASE_URL")
#     COS_REGION = os.getenv("COS_REGION")
#     COS_BUCKET = os.getenv("COS_BUCKET")


#     # DB_URL
#     DB_URL = "postgres://{}:{}@{}:{}/{}".format(
#         os.getenv("DB_USER"),
#         os.getenv("DB_PASSWORD"),
#         os.getenv("DB_SERVER"),
#         os.getenv("DB_PORT"),
#         os.getenv("DB_NAME"),
#     )

#     # REDIS
#     REDIS_HOST: str = os.getenv("REDIS_HOST", "127.0.0.1")
#     REDIS_PORT = os.getenv("REDIS_PORT", 6379)
#     REDIS_PASSWORD = os.getenv("REDIS_PASSWORD")

#     class Config:
#         env_prefix = "APP_"
#         env_file = ".env"
#         env_file_encoding = "utf-8"

# settings = Configs()
