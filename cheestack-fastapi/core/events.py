# -*- coding:utf-8 -*-
"""
@Created on : 2022/4/22 22:02
@Author: binku<PERSON>
@Des: fastapi事件监听
"""

from typing import Callable
from fastapi import FastAPI
from core.postgresql import register_database


def startup(app: FastAPI) -> Callable:
    """
    FastApi 启动完成事件
    :param app: FastAPI
    :return: start_app
    """

    async def app_start() -> None:
        # 注册数据库
        await register_database(app)
        # APP启动完成后触发
        print("fastapi已启动")
    return app_start


def stopping(app: FastAPI) -> Callable:
    """
    FastApi 停止事件
    :param app: FastAPI
    :return: stop_app
    """

    async def stop_app() -> None:
        # APP停止时触发
        print("fastapi已停止")
    return stop_app
