# -*- coding:utf-8 -*-
"""
@Time : 2022/5/4 10:54 PM
@Author: binku<PERSON>
@Des: 基础schemas
"""
from functools import lru_cache
from pydantic import BaseModel, create_model
from typing import Any, Optional
from core.http import HttpCode
from typing import get_origin, get_args


class BaseResponseModel(BaseModel):
    code: int = HttpCode.SUCCESS
    msg: str = "success"
    data: Any = []


# 当函数被调用时，lru_cache 会检查是否已经用相同的参数调用过这个函数。
# 如果是，它会直接返回缓存的结果，而不是重新执行函数。
# 如果不是，它会执行函数，缓存结果，然后返回结果。
@lru_cache(maxsize=None)
def resmod(model: type[BaseModel]):
    """创建返回的pydantic模型
    @param name: 模型名
    @param model: 模型
    @return: pydantic模型
    """

    # if get_origin(model) is None:
    #     return BaseResponseModel
    # # 检查是否为List类型
    if get_origin(model) is list:
        # elif get_origin(model) is list:
        # 获取List中的实际模型类型
        inner_model = get_args(model)[0]
        name = f"{inner_model.__name__}ListResponse"
    else:
        name = f"{model.__name__}Response"
        # 创建新的响应类，继承自JSONResponse
    ResponseModel = create_model(
        name,
        __base__=BaseResponseModel,
        data=(model, ...),
    )
    return ResponseModel


class WebsocketMessage(BaseModel):
    action: Optional[str]
    user: Optional[int]
    data: Optional[Any]


class WechatOAuthData(BaseModel):
    access_token: str
    expires_in: int
    refresh_token: str
    unionid: Optional[str]
    scope: str
    openid: str


class WechatUserInfo(BaseModel):
    openid: str
    nickname: str
    sex: int
    city: str
    province: str
    country: str
    headimgurl: str
    unionid: Optional[str]
