from typing import Any, Union
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from tortoise.exceptions import DoesNotExist, IntegrityError, OperationalError
from tortoise.exceptions import ValidationError as MysqlValidationError
from sutils.log import Slog


# 异常错误处理
# 自定义的exception
def add_exception_handle(app: FastAPI):
    app.add_exception_handler(HTTPException, http_error_handler)
    app.add_exception_handler(RequestValidationError, http422_error_handler)
    app.add_exception_handler(DoesNotExist, mysql_does_not_exist)
    app.add_exception_handler(IntegrityError, mysql_integrity_error)
    app.add_exception_handler(MysqlValidationError, mysql_validation_error)
    app.add_exception_handler(OperationalError, mysql_operational_error)
    app.add_exception_handler(Sex<PERSON>, sexception_handler)
    app.add_exception_handler(Exception, global_exception_handler)


async def global_exception_handler(request: Request, exc: Exception):
    print(f"Unhandled exception: {str(exc)}")
    Slog.debug(exc.__str__())
    return JSONResponse(
        {"code": -1, "msg": "服务器错误", "data": []},
        status_code=500,
    )


async def mysql_validation_error(_: Request, exc: MysqlValidationError):
    """
    数据库字段验证错误
    :param _:
    :param exc:
    :return:
    """
    Slog.debug(exc.__str__())
    return JSONResponse({"code": -1, "msg": exc.__str__(), "data": []}, status_code=422)


async def mysql_integrity_error(_: Request, exc: IntegrityError):
    """
    完整性错误
    :param _:
    :param exc:
    :return:
    """
    Slog.debug(exc.__str__())
    return JSONResponse({"code": -1, "msg": exc.__str__(), "data": []}, status_code=422)


async def mysql_does_not_exist(_: Request, exc: DoesNotExist):
    """
    mysql 查询对象不存在异常处理
    :param _:
    :param exc:
    :return:
    """
    Slog.debug(exc.__str__())
    return JSONResponse({"code": -1, "msg": "数据操作失败", "data": []}, status_code=500)


async def mysql_operational_error(_: Request, exc: OperationalError):
    """
    mysql 数据库异常错误处理
    :param _:
    :param exc:
    :return:
    """
    Slog.debug(exc.__str__())
    return JSONResponse({"code": -1, "msg": "数据操作失败", "data": []}, status_code=500)


async def http_error_handler(_: Request, exc: HTTPException):
    """
    http异常处理
    :param _:
    :param exc:
    :return:
    """
    Slog.debug(exc.__str__())
    return JSONResponse(
        {"code": -1, "msg": exc.detail, "data": []},
        status_code=exc.status_code,
        headers=exc.headers,
    )


# 定义一个`CstException`异常类, 用于抛出自定义异常
# 用法: raise CstException(msg="some error")
class Sexception(Exception):
    def __init__(self, code=-1, msg="failed", data: Any = [], status_code=400):
        """
        失败返回格式
        :param code:
        :param errmsg:
        """
        self.code = code
        self.msg = msg
        self.data = data
        self.status_code = status_code


async def sexception_handler(_: Request, exc: Sexception):
    """
    根据自定义的`ChvException`异常, 定义一个异常处理函数, 用于返回自定义异常
    注意: 这里的`ChvException`异常类, 必须在fastapi中注册使用, 否则无法捕获
    例如: app.add_exception_handler(exception.ChvException, exception.chv_exception)
    """
    return JSONResponse(
        {
            "code": exc.code,
            "msg": exc.msg,
            "data": exc.data,
        },
        status_code=exc.status_code,
    )


async def http422_error_handler(
    _: Request,
    exc: Union[RequestValidationError, ValidationError],
) -> JSONResponse:
    """
    参数校验错误处理
    :param _:
    :param exc:
    :return:
    """

    Slog.debug(f"ValidationError: {exc.__str__()}")

    return JSONResponse(
        {
            "code": status.HTTP_422_UNPROCESSABLE_ENTITY,
            "msg": "数据校验错误",
            "data": exc.errors(),
        },
        status_code=422,
    )
