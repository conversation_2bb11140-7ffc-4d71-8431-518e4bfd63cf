from fastapi import FastAPI
from tortoise.contrib.fastapi import register_tortoise
from core.config import settings

TORTOISE_ORM = {
    "connections": {
        "master": settings.DATABASE_URL,
        "slave": settings.SLAVE_DATABASE_URL,  # 添加从数据库连接
    },
    "apps": {
        "models": {
            # appname设置为models
            "models": [
                # “models”是存储model模型文件的路径，例如`apps.auth.models`,可以添加多个文件
                "apps.auth.models",
                "apps.study.models",
                "apps.general.models",
                "apps.pronunciation.models",
                # 使用`aerich`时必须添加`aerich.models`
                "aerich.models",
            ],
            "default_connection": "master",
        },
    },
    "routers": ["core.routers.Router"],
    "use_tz": True,
    "timezone": "UTC",
}


async def register_database(app: FastAPI):
    # # 注册数据库
    register_tortoise(
        app,
        config=TORTOISE_ORM,
        generate_schemas=False,  # 主从必须设置为True
        add_exception_handlers=True,
    )
