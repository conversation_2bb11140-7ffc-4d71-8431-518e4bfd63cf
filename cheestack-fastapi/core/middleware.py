import time
from fastapi import <PERSON><PERSON><PERSON>, Request
from starlette.datastructures import MutableHeaders
from starlette.types import ASGIApp, Message, Receive, Scope, Send
from sutils.string import Sstring
from starlette.middleware.sessions import SessionMiddleware
from apps.auth.config import auth_settings


def add_middleware(app: FastAPI):
    # 添加自定义的基础中间键
    app.add_middleware(BaseMiddleware)
    # 添加session中间键, 需要返回cookie时使用
    app.add_middleware(
        # from starlette.middleware.sessions import SessionMiddleware
        SessionMiddleware,
        secret_key=auth_settings.SESSION_SECRET_KEY,
        session_cookie=auth_settings.SESSION_COOKIE,
        max_age=auth_settings.SESSION_MAX_AGE,
    )


""" 
基础的中间键, 主要处理好`session`
"""


class BaseMiddleware:
    """
    Middleware
    """

    def __init__(
        self,
        app: ASGIApp,
    ) -> None:
        self.app = app

    async def __call__(self, scope: Scope, receive: Receive, send: Send) -> None:
        # 如果请求类型不是`http`, 直接返回
        if scope["type"] != "http":  # 非http协议
            await self.app(scope, receive, send)
            return
        # 记录开始处理的时间
        start_time = time.time()
        # 创建Request请求实例
        req = Request(scope, receive, send)
        # 如果没有获取到`session`, 则添加一个`session`
        if not req.session.get("session"):
            # 这里测试用, 用了一个随机数
            req.session.setdefault("session", Sstring.unique_str())

        # 如果已有`session`
        async def send_wrapper(message: Message) -> None:
            process_time = time.time() - start_time
            if message["type"] == "http.response.start":
                headers = MutableHeaders(scope=message)
                headers.append("X-Process-Time", str(process_time))
            await send(message)

        await self.app(scope, receive, send_wrapper)
