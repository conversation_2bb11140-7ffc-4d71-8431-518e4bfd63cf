"""
日志配置模块
配置结构化日志系统
"""

import sys
from pathlib import Path
from typing import Dict, Any

from loguru import logger


def setup_logging(
    log_level: str = "INFO",
    log_file: str = "logs/cheestack.log",
    rotation: str = "10 MB",
    retention: str = "30 days",
    compression: str = "zip"
) -> None:
    """
    配置日志系统
    
    Args:
        log_level: 日志级别
        log_file: 日志文件路径
        rotation: 日志轮转大小
        retention: 日志保留时间
        compression: 压缩格式
    """
    # 移除默认处理器
    logger.remove()
    
    # 控制台输出格式
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 文件输出格式
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        format=console_format,
        level=log_level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 创建日志目录
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 添加文件处理器
    logger.add(
        log_file,
        format=file_format,
        level=log_level,
        rotation=rotation,
        retention=retention,
        compression=compression,
        backtrace=True,
        diagnose=True,
        encoding="utf-8"
    )
    
    # 添加错误日志文件处理器
    error_log_file = log_path.parent / "error.log"
    logger.add(
        str(error_log_file),
        format=file_format,
        level="ERROR",
        rotation=rotation,
        retention=retention,
        compression=compression,
        backtrace=True,
        diagnose=True,
        encoding="utf-8"
    )


def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger


class LoggerMixin:
    """日志混入类"""
    
    @property
    def logger(self):
        """获取当前类的日志记录器"""
        return get_logger(self.__class__.__name__)


def log_function_call(func_name: str, args: Dict[str, Any] = None, kwargs: Dict[str, Any] = None):
    """记录函数调用日志"""
    args_str = f"args={args}" if args else ""
    kwargs_str = f"kwargs={kwargs}" if kwargs else ""
    params = ", ".join(filter(None, [args_str, kwargs_str]))
    logger.debug(f"调用函数: {func_name}({params})")


def log_api_request(method: str, path: str, params: Dict[str, Any] = None, body: Dict[str, Any] = None):
    """记录API请求日志"""
    params_str = f"params={params}" if params else ""
    body_str = f"body={body}" if body else ""
    details = ", ".join(filter(None, [params_str, body_str]))
    logger.info(f"API请求: {method} {path} | {details}")


def log_api_response(method: str, path: str, status_code: int, response_time: float):
    """记录API响应日志"""
    logger.info(f"API响应: {method} {path} | 状态码: {status_code} | 耗时: {response_time:.3f}s")


def log_database_query(query: str, params: Dict[str, Any] = None, execution_time: float = None):
    """记录数据库查询日志"""
    params_str = f"params={params}" if params else ""
    time_str = f"耗时: {execution_time:.3f}s" if execution_time else ""
    details = ", ".join(filter(None, [params_str, time_str]))
    logger.debug(f"数据库查询: {query} | {details}")


def log_external_api_call(service: str, endpoint: str, method: str, response_time: float, status_code: int):
    """记录外部API调用日志"""
    logger.info(
        f"外部API调用: {service} | {method} {endpoint} | "
        f"状态码: {status_code} | 耗时: {response_time:.3f}s"
    )


def log_user_action(user_id: str, action: str, details: Dict[str, Any] = None):
    """记录用户操作日志"""
    details_str = f"详情: {details}" if details else ""
    logger.info(f"用户操作: 用户ID={user_id} | 操作={action} | {details_str}")


def log_security_event(event_type: str, user_id: str = None, ip_address: str = None, details: Dict[str, Any] = None):
    """记录安全事件日志"""
    user_str = f"用户ID={user_id}" if user_id else ""
    ip_str = f"IP={ip_address}" if ip_address else ""
    details_str = f"详情={details}" if details else ""
    info = ", ".join(filter(None, [user_str, ip_str, details_str]))
    logger.warning(f"安全事件: {event_type} | {info}")


def log_performance_metric(metric_name: str, value: float, unit: str = "", tags: Dict[str, str] = None):
    """记录性能指标日志"""
    tags_str = f"标签={tags}" if tags else ""
    unit_str = f" {unit}" if unit else ""
    logger.info(f"性能指标: {metric_name}={value}{unit_str} | {tags_str}")


# 预配置的日志记录器
api_logger = get_logger("API")
db_logger = get_logger("DATABASE")
auth_logger = get_logger("AUTH")
business_logger = get_logger("BUSINESS")
performance_logger = get_logger("PERFORMANCE")