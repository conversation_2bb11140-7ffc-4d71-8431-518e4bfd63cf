import os
from pathlib import Path
from fastapi.templating import Jinja2Templates
from pydantic_settings import BaseSettings

class Config(BaseSettings):
    APP_VERSION: str = "1.0.0"
    WORKDIR: str = Path(__file__).parent.parent.as_posix()
    # # 数据库
    # DATABASE_URL: str = "postgres://postgres:CqmygYsdss0756@127.0.0.1:5501/cheestack"
    # SLAVE_DATABASE_URL: str = "postgres://postgres:CqmygYsdss0756@127.0.0.1:5502/cheestack"
    DATABASE_URL: str = "postgres://lyuscott:test1234@127.0.0.1:5432/cheestack"
    SLAVE_DATABASE_URL: str = "postgres://lyuscott:test1234@127.0.0.1:5432/cheestack"

    REDIS_URL: str = "redis://:Cqmygysdssredis0756@127.0.0.1:5505/0"
    REDIS_URL_CLIENT: str = "redis://:Cqmygysdssredis0756@127.0.0.1:5506/0"
    # # 环境配置
    # ENVIRONMENT: Environment = Environment.PRODUCTION
    # # 域名
    # SITE_DOMAIN: str = "chivetech.com"
    # # 这是允许跨域请求的源列表，用于配置 CORS (Cross-Origin Resource Sharing)
    # CORS_ORIGINS: list[str]
    # # 用于匹配允许跨域请求的源的正则表达式（可选）
    # CORS_ORIGINS_REGEX: str | None = None
    # # 允许的跨域请求头列表
    # CORS_HEADERS: list[str]
    # # 静态资源目录
    STATIC_DIR: str = os.path.join(WORKDIR, "static")
    TEMPLATE_DIR: str = os.path.join(STATIC_DIR, "templates")
    # # 渲染模板templates
    TEMPLATES: Jinja2Templates = Jinja2Templates(directory=TEMPLATE_DIR)
    # class Config:
    #     # env_prefix = "APP_"
    #     env_file = ".env"
    #     env_file_encoding = "utf-8"
    #     extra = "ignore"  # 忽略额外的字段


settings = Config()
