#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云短信服务测试脚本
用于诊断短信发送问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from apps.aliyun.alisms import AliSms
from apps.aliyun.config import ali_settings
from sutils.log import Slog
import json

def test_sms_config():
    """测试短信配置"""
    print("=== 阿里云短信配置检查 ===")
    print(f"AccessKey ID: {ali_settings.SMS_ACCESS_KEY_ID[:8]}***")
    print(f"AccessKey Secret: {ali_settings.SMS_ACCESS_KEY_SECRET[:8]}***")
    print(f"签名: {ali_settings.SMS_SIGN_NAME}")
    print(f"模板代码: {ali_settings.SMS_TEMPLATE_CODE}")
    print()

def test_sms_send(mobile: str = "13800138000"):
    """测试短信发送"""
    print("=== 阿里云短信发送测试 ===")
    print(f"测试手机号: {mobile}")
    
    try:
        # 发送测试短信
        result = AliSms.send(mobile, "1234")
        
        if result:
            print(f"✅ 短信发送成功，验证码: {result}")
        else:
            print("❌ 短信发送失败")
            
    except Exception as e:
        print(f"❌ 短信发送异常: {e}")
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}")

def test_sms_client():
    """测试短信客户端连接"""
    print("=== 阿里云短信客户端测试 ===")
    
    try:
        # 测试客户端是否正常初始化
        client = AliSms.client
        print(f"✅ 短信客户端初始化成功: {type(client)}")
        
        # 测试配置
        print(f"签名: {AliSms.sign_name}")
        print(f"模板代码: {AliSms.template_code}")
        
    except Exception as e:
        print(f"❌ 短信客户端测试失败: {e}")

def check_quota_and_balance():
    """检查短信余额和配额（如果API支持）"""
    print("=== 短信余额和配额检查 ===")
    print("注意: 阿里云短信API不直接提供余额查询，请登录阿里云控制台查看")
    print("控制台地址: https://dysms.console.aliyun.com/")
    print()

def main():
    """主函数"""
    print("阿里云短信服务诊断工具")
    print("=" * 50)
    
    # 检查配置
    test_sms_config()
    
    # 测试客户端
    test_sms_client()
    
    # 检查余额提示
    check_quota_and_balance()
    
    # 询问是否发送测试短信
    mobile = input("请输入测试手机号（直接回车跳过测试发送）: ").strip()
    if mobile:
        test_sms_send(mobile)
    else:
        print("跳过短信发送测试")
    
    print("\n=== 诊断完成 ===")
    print("如果发现问题，请检查：")
    print("1. 阿里云账户余额是否充足")
    print("2. AccessKey是否有效且有短信发送权限")
    print("3. 短信签名是否已审核通过")
    print("4. 短信模板是否已审核通过")
    print("5. 手机号格式是否正确")
    print("6. 是否触发了发送频率限制")

if __name__ == "__main__":
    main()
