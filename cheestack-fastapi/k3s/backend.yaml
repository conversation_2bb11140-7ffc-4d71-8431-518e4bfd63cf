apiVersion: apps/v1
kind: Deployment
metadata:
  name: cheestack-backend
  namespace: cheestack
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cheestack-backend
  template:
    metadata:
      labels:
        app: cheestack-backend
    spec:
      containers:
      - name: backend
        image: cheestack/fastapi:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: cheestack-config
        env:
        - name: DATABASE_USER
          valueFrom:
            secretKeyRef:
              name: cheestack-secret
              key: DATABASE_USER
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: cheestack-secret
              key: DATABASE_PASSWORD
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: cheestack-secret
              key: REDIS_PASSWORD
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: cheestack-secret
              key: JWT_SECRET_KEY
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: cheestack-secret
              key: SECRET_KEY
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health/
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health/
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: cheestack-backend-service
  namespace: cheestack
spec:
  selector:
    app: cheestack-backend
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cheestack-ingress
  namespace: cheestack
  annotations:
    kubernetes.io/ingress.class: traefik
    traefik.ingress.kubernetes.io/router.middlewares: cheestack-cors@kubernetescrd
spec:
  rules:
  - host: api.cheestack.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cheestack-backend-service
            port:
              number: 8000
