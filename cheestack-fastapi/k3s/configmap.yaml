apiVersion: v1
kind: ConfigMap
metadata:
  name: cheestack-config
  namespace: cheestack
data:
  APP_NAME: "CheeStack API"
  APP_VERSION: "2.0.0"
  DEBUG: "false"
  API_V1_PREFIX: "/api/v1"
  RATE_LIMIT_ENABLED: "true"
  RATE_LIMIT_REQUESTS: "100"
  RATE_LIMIT_PERIOD: "60"
  LOG_LEVEL: "INFO"
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "cheestack"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
