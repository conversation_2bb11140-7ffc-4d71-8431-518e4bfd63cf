#!/bin/bash

# CheeStack 后端部署脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 构建Docker镜像
build_image() {
    log_info "构建Docker镜像..."

    cd "$(dirname "$0")/.."

    # 构建镜像
    docker build -f docker/Dockerfile -t cheestack/fastapi:latest .
    
    # 如果有私有仓库，推送镜像
    if [ ! -z "$DOCKER_REGISTRY" ]; then
        docker tag cheestack/fastapi:latest $DOCKER_REGISTRY/cheestack/fastapi:latest
        docker push $DOCKER_REGISTRY/cheestack/fastapi:latest
    fi
    
    log_info "Docker镜像构建完成"
}

# 部署到K3s
deploy_k3s() {
    log_info "部署到K3s集群..."
    
    cd "$(dirname "$0")/../k3s"
    
    # 创建命名空间
    kubectl apply -f namespace.yaml
    
    # 应用配置
    kubectl apply -f configmap.yaml
    kubectl apply -f secret.yaml
    
    # 部署数据库
    kubectl apply -f postgres.yaml
    kubectl apply -f redis.yaml
    
    # 等待数据库就绪
    log_info "等待数据库就绪..."
    kubectl wait --for=condition=ready pod -l app=postgres -n cheestack --timeout=300s
    kubectl wait --for=condition=ready pod -l app=redis -n cheestack --timeout=300s
    
    # 部署后端服务
    kubectl apply -f backend.yaml
    
    # 等待后端就绪
    log_info "等待后端服务就绪..."
    kubectl wait --for=condition=ready pod -l app=cheestack-backend -n cheestack --timeout=300s
    
    log_info "K3s部署完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 获取后端Pod名称
    BACKEND_POD=$(kubectl get pods -n cheestack -l app=cheestack-backend -o jsonpath='{.items[0].metadata.name}')
    
    if [ -z "$BACKEND_POD" ]; then
        log_error "未找到后端Pod"
        exit 1
    fi
    
    # 运行迁移
    kubectl exec -n cheestack $BACKEND_POD -- aerich upgrade
    
    log_info "数据库迁移完成"
}

# 检查部署状态
check_status() {
    log_info "检查部署状态..."
    
    echo "Pods状态:"
    kubectl get pods -n cheestack
    
    echo -e "\nServices状态:"
    kubectl get services -n cheestack
    
    echo -e "\nIngress状态:"
    kubectl get ingress -n cheestack
    
    # 检查健康状态
    log_info "检查服务健康状态..."
    BACKEND_POD=$(kubectl get pods -n cheestack -l app=cheestack-backend -o jsonpath='{.items[0].metadata.name}')
    
    if [ ! -z "$BACKEND_POD" ]; then
        kubectl exec -n cheestack $BACKEND_POD -- curl -f http://localhost:8000/api/v1/health/ || log_warn "健康检查失败"
    fi
}

# 清理部署
cleanup() {
    log_warn "清理K3s部署..."
    
    cd "$(dirname "$0")/../k3s"
    
    kubectl delete -f backend.yaml --ignore-not-found=true
    kubectl delete -f redis.yaml --ignore-not-found=true
    kubectl delete -f postgres.yaml --ignore-not-found=true
    kubectl delete -f secret.yaml --ignore-not-found=true
    kubectl delete -f configmap.yaml --ignore-not-found=true
    kubectl delete -f namespace.yaml --ignore-not-found=true
    
    log_info "清理完成"
}

# 主函数
main() {
    case "$1" in
        "build")
            check_dependencies
            build_image
            ;;
        "deploy")
            check_dependencies
            build_image
            deploy_k3s
            run_migrations
            check_status
            ;;
        "status")
            check_status
            ;;
        "migrate")
            run_migrations
            ;;
        "cleanup")
            cleanup
            ;;
        *)
            echo "用法: $0 {build|deploy|status|migrate|cleanup}"
            echo "  build   - 构建Docker镜像"
            echo "  deploy  - 完整部署到K3s"
            echo "  status  - 检查部署状态"
            echo "  migrate - 运行数据库迁移"
            echo "  cleanup - 清理部署"
            exit 1
            ;;
    esac
}

main "$@"
