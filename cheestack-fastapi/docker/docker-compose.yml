version: '3.8'

services:
  # FastAPI 后端服务
  backend:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: cheestack-backend
    ports:
      - "8000:8000"
    environment:
      - DEBUG=false
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USER=cheestack
      - DATABASE_PASSWORD=cheestack123
      - DATABASE_NAME=cheestack
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ../app:/app/app:ro
      - backend_logs:/app/logs
    networks:
      - cheestack-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: cheestack-postgres
    environment:
      - POSTGRES_DB=cheestack
      - POSTGRES_USER=cheestack
      - POSTGRES_PASSWORD=cheestack123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - cheestack-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cheestack -d cheestack"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: cheestack-redis
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - cheestack-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: cheestack-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
    networks:
      - cheestack-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  cheestack-network:
    driver: bridge
