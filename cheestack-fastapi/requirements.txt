#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile requirements.in
#
aerich==0.7.2
    # via -r requirements.in
aiohttp==3.9.5
    # via
    #   alibabacloud-tea
    #   edge-tts
aiosignal==1.3.1
    # via aiohttp
aiosqlite==0.17.0
    # via tortoise-orm
alibabacloud-credentials==0.3.4
    # via
    #   alibabacloud-gateway-spi
    #   alibabacloud-tea-openapi
alibabacloud-dysmsapi20170525==2.0.24
    # via -r requirements.in
alibabacloud-endpoint-util==0.0.3
    # via alibabacloud-dysmsapi20170525
alibabacloud-gateway-spi==0.0.1
    # via alibabacloud-tea-openapi
alibabacloud-openapi-util==0.2.2
    # via
    #   alibabacloud-dysmsapi20170525
    #   alibabacloud-tea-openapi
alibabacloud-tea==0.3.9
    # via
    #   alibabacloud-credentials
    #   alibabacloud-endpoint-util
    #   alibabacloud-tea-util
    #   alibabacloud-tea-xml
alibabacloud-tea-openapi==0.3.10
    # via alibabacloud-dysmsapi20170525
alibabacloud-tea-util==0.3.13
    # via
    #   alibabacloud-dysmsapi20170525
    #   alibabacloud-openapi-util
    #   alibabacloud-tea-openapi
alibabacloud-tea-xml==0.0.2
    # via alibabacloud-tea-openapi
annotated-types==0.7.0
    # via pydantic
anyio==4.4.0
    # via
    #   httpx
    #   starlette
    #   watchfiles
arrow==1.3.0
    # via -r requirements.in
asyncpg==0.29.0
    # via -r requirements.in
attrs==23.2.0
    # via aiohttp
azure-cognitiveservices-speech==1.41.1
    # via -r requirements.in
certifi==2024.7.4
    # via
    #   edge-tts
    #   httpcore
    #   httpx
    #   requests
cffi==1.16.0
    # via cryptography
charset-normalizer==3.3.2
    # via requests
click==8.1.7
    # via
    #   aerich
    #   typer
    #   uvicorn
cos-python-sdk-v5==1.9.27
    # via -r requirements.in
crcmod==1.7
    # via cos-python-sdk-v5
cryptography==43.0.0
    # via alibabacloud-openapi-util
dictdiffer==0.9.0
    # via aerich
dnspython==2.6.1
    # via email-validator
edge-tts==6.1.15
    # via -r requirements.in
email-validator==2.2.0
    # via fastapi
fastapi==0.111.1
    # via -r requirements.in
fastapi-cli==0.0.4
    # via fastapi
frozenlist==1.4.1
    # via
    #   aiohttp
    #   aiosignal
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.5
    # via httpx
httptools==0.6.1
    # via uvicorn
httpx==0.27.0
    # via
    #   -r requirements.in
    #   fastapi
    #   ollama
idna==3.7
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
    #   yarl
iniconfig==2.0.0
    # via pytest
iso8601==1.1.0
    # via tortoise-orm
itsdangerous==2.2.0
    # via -r requirements.in
jinja2==3.1.4
    # via fastapi
markdown-it-py==3.0.0
    # via rich
markupsafe==2.1.5
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
multidict==6.0.5
    # via
    #   aiohttp
    #   yarl
ollama==0.3.3
    # via -r requirements.in
packaging==24.1
    # via pytest
passlib==1.7.4
    # via -r requirements.in
pluggy==1.5.0
    # via pytest
pycparser==2.22
    # via cffi
pycryptodome==3.20.0
    # via cos-python-sdk-v5
pydantic==2.8.2
    # via
    #   -r requirements.in
    #   aerich
    #   fastapi
    #   pydantic-settings
    #   tortoise-orm
pydantic-core==2.20.1
    # via pydantic
pydantic-settings==2.4.0
    # via -r requirements.in
pygments==2.18.0
    # via rich
pyjwt==2.8.0
    # via -r requirements.in
pypika-tortoise==0.1.6
    # via tortoise-orm
pytest==8.3.2
    # via
    #   -r requirements.in
    #   pytest-asyncio
pytest-asyncio==0.23.8
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via arrow
python-dotenv==1.0.1
    # via
    #   -r requirements.in
    #   pydantic-settings
    #   uvicorn
python-multipart==0.0.9
    # via fastapi
pytz==2024.1
    # via tortoise-orm
pyyaml==6.0.1
    # via uvicorn
qcloud-python-sts==3.1.6
    # via -r requirements.in
redis==3.5.3
    # via -r requirements.in
requests==2.32.3
    # via
    #   alibabacloud-tea
    #   cos-python-sdk-v5
    #   qcloud-python-sts
rich==13.7.1
    # via typer
shellingham==1.5.4
    # via typer
shortuuid==1.0.13
    # via -r requirements.in
six==1.16.0
    # via
    #   cos-python-sdk-v5
    #   python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   httpx
starlette==0.37.2
    # via fastapi
tomlkit==0.13.0
    # via aerich
tortoise-orm==0.21.5
    # via
    #   -r requirements.in
    #   aerich
typer==0.12.3
    # via fastapi-cli
types-python-dateutil==2.9.0.20240316
    # via arrow
typing-extensions==4.12.2
    # via
    #   aiosqlite
    #   fastapi
    #   pydantic
    #   pydantic-core
    #   typer
urllib3==2.2.2
    # via requests
uvicorn[standard]==0.30.3
    # via
    #   -r requirements.in
    #   fastapi
uvloop==0.19.0
    # via uvicorn
watchfiles==0.22.0
    # via uvicorn
websockets==12.0
    # via uvicorn
xmltodict==0.13.0
    # via cos-python-sdk-v5
yarl==1.9.4
    # via aiohttp
