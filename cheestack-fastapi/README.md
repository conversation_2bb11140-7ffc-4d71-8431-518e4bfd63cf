# CheeStack FastAPI 后端

> 基于 FastAPI 的智能学习平台后端服务

## 📖 项目概述

### 功能简介
CheeStack FastAPI 后端是一个高性能的异步Web服务，为智能学习平台提供完整的API支持。基于间隔重复学习算法，支持多媒体学习卡片、用户认证、数据同步等核心功能。

### 核心特性
- ✅ **高性能异步框架** - FastAPI + Tortoise ORM
- ✅ **完整用户系统** - JWT认证 + 权限管理 + 设备管理
- ✅ **智能学习算法** - 间隔重复学习系统 (SRS)
- ✅ **多媒体支持** - 图片、音频、视频处理
- 🚧 **数据同步机制** - 多设备数据同步 (架构设计完成)
- ✅ **AI服务集成** - TTS、语音识别、OCR等

### 技术实现
- **框架**: FastAPI 0.111.1 (异步Web框架)
- **数据库**: PostgreSQL 13+ (主数据库) + Redis 6.0+ (缓存)
- **ORM**: Tortoise ORM (异步Python ORM)
- **认证**: JWT + 手机验证码认证
- **文件存储**: 腾讯云COS对象存储
- **部署**: Docker + Uvicorn

## 🏗️ 架构设计

### 核心组件
```
cheestack-fastapi/
├── main.py                     # 应用入口
├── core/                       # 核心功能模块
│   ├── config.py               # 配置管理
│   ├── models.py               # 基础数据模型
│   ├── exceptions.py           # 异常处理
│   ├── middleware.py           # 中间件
│   ├── routers.py              # 路由汇总
│   └── responses.py            # 响应格式化
├── apps/                       # 业务应用模块
│   ├── auth/                   # 用户认证 + 设备管理
│   ├── sync/                   # 数据同步服务 (规划中)
│   ├── study/                  # 学习系统 (原版)
│   ├── learn/                  # 学习系统 (优化版)
│   ├── general/                # 通用功能
│   ├── pronunciation/          # 发音功能
│   ├── aliyun/                 # 阿里云服务集成
│   └── tcyun/                  # 腾讯云服务集成
├── sutils/                     # 工具函数库
├── migrations/                 # 数据库迁移文件
└── tests/                      # 测试文件
```

### 数据流
```
HTTP请求 → FastAPI路由 → 中间件处理 → 业务逻辑 → 数据库操作 → 响应返回
```

## 🚀 快速开始

### 环境要求
- Python >= 3.12
- PostgreSQL >= 13
- Redis >= 6.0
- 腾讯云COS (可选，用于文件存储)

### 安装依赖
```bash
cd cheestack-fastapi
pip install -r requirements.txt
```

### 环境配置
创建 `.env` 文件：
```env
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/cheestack
REDIS_URL=redis://localhost:6379/0

# JWT配置
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRE_HOURS=24

# 腾讯云配置 (可选)
TENCENT_SECRET_ID=your-secret-id
TENCENT_SECRET_KEY=your-secret-key
COS_BUCKET=your-bucket
COS_REGION=ap-beijing

# 短信服务配置 (可选)
SMS_ACCESS_KEY=your-access-key
SMS_SECRET_KEY=your-secret-key
```

### 数据库初始化
```bash
# 初始化数据库迁移
aerich init -t core.postgresql.TORTOISE_ORM

# 生成迁移文件
aerich init-db

# 执行迁移
aerich upgrade
```

### 启动服务
```bash
# 开发模式
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 生产模式
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 访问文档
- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc

## 🔌 API接口

### 主要端点
- `POST /api/v1/auth/login` - 用户登录
- `GET /v1/books` - 获取书籍列表
- `POST /api/v1/cards` - 创建学习卡片
- `GET /v1/books/{id}/study` - 获取学习内容
- `POST /api/v1/study/record` - 提交学习记录

### 请求示例
```python
import requests

# 用户登录
response = requests.post('http://localhost:8000/api/v1/auth/login', json={
    'mobile': '13800138000',
    'password': 'your-password'
})
token = response.json()['data']['access_token']

# 获取书籍列表
headers = {'Authorization': f'Bearer {token}'}
books = requests.get('http://localhost:8000/v1/books', headers=headers)
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_auth.py -v

# 运行测试并生成覆盖率报告
pytest --cov=apps --cov-report=html
```

### 测试覆盖
- 单元测试: 核心业务逻辑测试
- 集成测试: API接口测试
- 数据库测试: 数据模型和查询测试

## 🔧 依赖关系

### 核心依赖
- **fastapi**: Web框架核心
- **tortoise-orm**: 异步ORM
- **asyncpg**: PostgreSQL异步驱动
- **redis**: Redis客户端
- **pydantic**: 数据验证
- **pyjwt**: JWT认证

### 外部依赖
- **httpx**: HTTP客户端
- **aiohttp**: 异步HTTP处理
- **cos-python-sdk-v5**: 腾讯云COS
- **edge-tts**: 语音合成
- **ollama**: AI模型集成

### 内部依赖
- `core`: 框架核心功能
- `sutils`: 工具函数库
- `apps.auth`: 用户认证模块
- `apps.study`: 学习系统模块

## 📊 性能优化

### 数据库优化
- 使用连接池管理数据库连接
- 合理使用索引和查询优化
- 使用 `prefetch_related()` 预加载关联数据
- 批量操作减少数据库访问次数

### 缓存策略
- Redis缓存热点数据
- 用户会话信息缓存
- 学习统计数据缓存
- API响应结果缓存

### 异步处理
- 异步数据库操作
- 异步文件上传处理
- 后台任务队列处理
- 异步HTTP请求

## 🔒 安全考虑

### 认证授权
- JWT Token认证机制
- 基于角色的权限控制 (RBAC)
- API访问频率限制
- 用户数据隔离保护

### 数据验证
- Pydantic模型严格验证输入数据
- SQL注入防护 (ORM参数化查询)
- XSS攻击防护
- 文件上传类型和大小限制

### 传输安全
- HTTPS加密传输 (生产环境)
- 敏感数据加密存储
- 请求签名验证
- CORS跨域访问控制

## 🚀 部署指南

### Docker部署
```dockerfile
FROM python:3.12-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 生产环境配置
```bash
# 使用Gunicorn + Uvicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# 使用Nginx反向代理
# 配置SSL证书
# 配置负载均衡
```

## 📝 开发注意事项

### 重要规范
- 遵循FastAPI最佳实践
- 使用异步函数处理I/O操作
- 合理使用Pydantic模型验证
- 保持API接口向后兼容

### 常见问题
- **数据库连接**: 确保PostgreSQL和Redis服务正常运行
- **环境变量**: 检查.env文件配置是否正确
- **依赖冲突**: 使用虚拟环境隔离依赖
- **异步问题**: 注意async/await的正确使用

## 🔗 相关文档

### 产品和需求文档
- [PRD产品需求文档](../docs/PRD.md) - 完整产品需求规格说明
- [FastAPI开发规范](../.augment/rules/fastapi-rule.md) - 后端开发规范

### 开发实现文档
- [开发文档总览](./developments/README.md) - 开发实现文档目录
- [用户认证系统](./developments/01-user-authentication.md) - 用户认证实现
- [设备管理系统](./developments/02-device-management.md) - 设备管理实现
- [数据同步机制](./developments/09-data-sync.md) - 数据同步架构设计

### API和技术文档
- [API参考文档](./apps/auth/README.md) - 认证API接口文档
- [数据库迁移](./migrations/models/) - 数据库变更记录

---

**注意**: 本项目正在积极开发中，API接口可能会发生变化。建议在生产环境使用前进行充分测试。
