[tool:pytest]
minversion = 6.0
addopts = -ra -q --strict-markers --strict-config --cov=apps --cov=core --cov=sutils --cov-report=html --cov-report=term-missing
testpaths = tests
asyncio_mode = auto
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    auth: marks tests related to authentication
    books: marks tests related to books
    cards: marks tests related to cards
    study: marks tests related to study functionality