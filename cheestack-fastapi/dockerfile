FROM python:3.12.4-slim-bullseye
# 设置当前时区为`Shanghai`
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo 'Asia/Shanghai' >/etc/timezone
# 复制源apt国内源配置文件到docker内部
COPY assets/sources.list /etc/apt/sources.list
RUN apt update
# 设置工作目录
WORKDIR /code
# 复制该文件到工作目录中, /code/requirements.txt是docker内部自动生成的工作目录，不用自己创建的，请注意。
COPY requirements.txt .
# 禁用缓存并批量安装包(后面的链接是利用豆瓣源安装，速度会加快)
RUN pip install --no-cache-dir --upgrade -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
# 复制代码到工作目录
COPY . .

# 放开端口
EXPOSE 8080
# 命令行运行，启动uvicorn服务，指定ip和端口(--reload：让服务器在更新代码后重新启动。仅在开发时使用该选项。docker启动执行cmd命令)
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080", "--reload"]